import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import '../logger/logger_service.dart';

/// Enhanced Secure Storage Service for sensitive data
class SecureStorageService extends GetxService {
  static SecureStorageService get instance => Get.find<SecureStorageService>();

  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  @override
  void onInit() {
    super.onInit();
    LoggerService.info('SecureStorageService initialized', 'SECURE_STORAGE');
  }

  /// Store sensitive data
  Future<bool> write(String key, String value) async {
    try {
      await _storage.write(key: key, value: value);
      LoggerService.debug('Secure data written for key: $key', 'SECURE_STORAGE');
      return true;
    } catch (e) {
      LoggerService.error('Failed to write secure data for key: $key', e, null, 'SECURE_STORAGE');
      return false;
    }
  }

  /// Read sensitive data
  Future<String?> read(String key) async {
    try {
      final value = await _storage.read(key: key);
      LoggerService.debug('Secure data read for key: $key, hasValue: ${value != null}', 'SECURE_STORAGE');
      return value;
    } catch (e) {
      LoggerService.error('Failed to read secure data for key: $key', e, null, 'SECURE_STORAGE');
      return null;
    }
  }

  /// Delete sensitive data
  Future<bool> delete(String key) async {
    try {
      await _storage.delete(key: key);
      LoggerService.debug('Secure data deleted for key: $key', 'SECURE_STORAGE');
      return true;
    } catch (e) {
      LoggerService.error('Failed to delete secure data for key: $key', e, null, 'SECURE_STORAGE');
      return false;
    }
  }

  /// Clear all sensitive data
  Future<bool> deleteAll() async {
    try {
      await _storage.deleteAll();
      LoggerService.info('All secure data cleared', 'SECURE_STORAGE');
      return true;
    } catch (e) {
      LoggerService.error('Failed to clear all secure data', e, null, 'SECURE_STORAGE');
      return false;
    }
  }

  /// Check if key exists
  Future<bool> containsKey(String key) async {
    try {
      final exists = await _storage.containsKey(key: key);
      LoggerService.debug('Secure key exists check - Key: $key, Exists: $exists', 'SECURE_STORAGE');
      return exists;
    } catch (e) {
      LoggerService.error('Failed to check secure key existence: $key', e, null, 'SECURE_STORAGE');
      return false;
    }
  }

  /// Get all keys
  Future<List<String>> getAllKeys() async {
    try {
      final data = await _storage.readAll();
      final keys = data.keys.toList();
      LoggerService.debug('Retrieved ${keys.length} secure keys', 'SECURE_STORAGE');
      return keys;
    } catch (e) {
      LoggerService.error('Failed to get all secure keys', e, null, 'SECURE_STORAGE');
      return [];
    }
  }

  /// Get all data (use with caution)
  Future<Map<String, String>> readAll() async {
    try {
      final data = await _storage.readAll();
      LoggerService.debug('Retrieved all secure data (${data.length} items)', 'SECURE_STORAGE');
      return data;
    } catch (e) {
      LoggerService.error('Failed to read all secure data', e, null, 'SECURE_STORAGE');
      return {};
    }
  }

  /// Write multiple key-value pairs
  Future<bool> writeAll(Map<String, String> data) async {
    try {
      for (final entry in data.entries) {
        await _storage.write(key: entry.key, value: entry.value);
      }
      LoggerService.debug('Written ${data.length} secure items', 'SECURE_STORAGE');
      return true;
    } catch (e) {
      LoggerService.error('Failed to write multiple secure items', e, null, 'SECURE_STORAGE');
      return false;
    }
  }

  /// Read multiple keys at once
  Future<Map<String, String?>> readMultiple(List<String> keys) async {
    try {
      final result = <String, String?>{};
      for (final key in keys) {
        result[key] = await _storage.read(key: key);
      }
      LoggerService.debug('Read ${keys.length} secure items', 'SECURE_STORAGE');
      return result;
    } catch (e) {
      LoggerService.error('Failed to read multiple secure items', e, null, 'SECURE_STORAGE');
      return {};
    }
  }
}