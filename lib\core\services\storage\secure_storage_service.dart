import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecureStorageService {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Store sensitive data
  static Future<void> write(String key, String value) async {
    await _storage.write(key: key, value: value);
  }

  // Read sensitive data
  static Future<String?> read(String key) async {
    return await _storage.read(key: key);
  }

  // Delete sensitive data
  static Future<void> delete(String key) async {
    await _storage.delete(key: key);
  }

  // Clear all sensitive data
  static Future<void> deleteAll() async {
    await _storage.deleteAll();
  }

  // Check if key exists
  static Future<bool> containsKey(String key) async {
    return await _storage.containsKey(key: key);
  }

  // Get all keys
  static Future<Map<String, String>> readAll() async {
    return await _storage.readAll();
  }
}