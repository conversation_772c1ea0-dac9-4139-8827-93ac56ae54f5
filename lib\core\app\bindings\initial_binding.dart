import 'package:get/get.dart';
import '../../services/storage/local_storage_service.dart';
import '../../services/auth/auth_service.dart';
import '../../services/auth/oauth_service.dart';

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Storage services - Initialize first
    Get.put<LocalStorageService>(LocalStorageService(), permanent: true);
    
    // Auth services - OAuth service must be initialized before AuthService
    Get.put<OAuthService>(OAuthService(), permanent: true);
    Get.put<AuthService>(AuthService(), permanent: true);
  }
}