import 'package:get/get.dart';

// Core Services
import '../../services/logger/logger_service.dart';
import '../../services/storage/local_storage_service.dart';
import '../../services/storage/secure_storage_service.dart';
import '../../services/storage/database_service.dart';
import '../../services/connectivity/connectivity_service.dart';
import '../../services/error/error_handler_service.dart';
import '../../services/api/api_service.dart';
import '../../services/notification/notification_service.dart';

// Auth Services
import '../../services/auth/auth_service.dart';
import '../../services/auth/oauth_service.dart';

/// Initial binding that registers all core services
class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Core services - Initialize in order of dependency

    // 1. Logger service (needed by all other services)
    Get.put<LoggerService>(LoggerService(), permanent: true);

    // 2. Storage services
    Get.put<LocalStorageService>(LocalStorageService(), permanent: true);
    Get.put<SecureStorageService>(SecureStorageService(), permanent: true);
    Get.put<DatabaseService>(DatabaseService(), permanent: true);

    // 3. Connectivity service
    Get.put<ConnectivityService>(ConnectivityService(), permanent: true);

    // 4. Error handler service
    Get.put<ErrorHandlerService>(ErrorHandlerService(), permanent: true);

    // 5. API service (depends on connectivity and error handler)
    Get.put<ApiService>(ApiService(), permanent: true);

    // 6. Notification service
    Get.put<NotificationService>(NotificationService(), permanent: true);

    // 7. Auth services (OAuth service must be initialized before AuthService)
    Get.put<OAuthService>(OAuthService(), permanent: true);
    Get.put<AuthService>(AuthService(), permanent: true);
  }
}