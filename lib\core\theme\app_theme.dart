import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'app_colors.dart';
import 'app_typography.dart';
import 'app_dimensions.dart';

/// Main theme configuration for the app
class AppTheme {
  AppTheme._();

  /// Light theme configuration
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      
      // Color scheme
      colorScheme: const ColorScheme.light(
        primary: AppColors.periwinkleBase,
        onPrimary: AppColors.surface,
        primaryContainer: AppColors.periwinkleLight,
        onPrimaryContainer: AppColors.periwinkleDarker,
        
        secondary: AppColors.lavender,
        onSecondary: AppColors.textPrimary,
        secondaryContainer: AppColors.periwinkleLighter,
        onSecondaryContainer: AppColors.periwinkleDark,
        
        tertiary: AppColors.lilac,
        onTertiary: AppColors.surface,
        tertiaryContainer: AppColors.periwinkleLighter,
        onTertiaryContainer: AppColors.periwinkleDarker,
        
        error: AppColors.error,
        onError: AppColors.surface,
        errorContainer: Color(0xFFFFEDEA),
        onErrorContainer: Color(0xFF410002),
        
        background: AppColors.background,
        onBackground: AppColors.textPrimary,
        
        surface: AppColors.surface,
        onSurface: AppColors.textPrimary,
        surfaceVariant: AppColors.periwinkleLighter,
        onSurfaceVariant: AppColors.textSecondary,
        
        outline: AppColors.border,
        outlineVariant: AppColors.border,
        
        shadow: Colors.black,
        scrim: Colors.black54,
        
        inverseSurface: AppColors.textPrimary,
        onInverseSurface: AppColors.surface,
        inversePrimary: AppColors.periwinkleLight,
      ),

      // Primary swatch
      primarySwatch: AppColors.periwinkleSwatch,
      
      // Scaffold
      scaffoldBackgroundColor: AppColors.background,
      
      // App bar theme
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.headerBackground,
        foregroundColor: AppColors.textPrimary,
        elevation: 0,
        scrolledUnderElevation: 1,
        shadowColor: Colors.black.withOpacity(0.1),
        surfaceTintColor: Colors.transparent,
        titleTextStyle: AppTypography.heading2,
        toolbarTextStyle: AppTypography.bodyMedium,
        iconTheme: const IconThemeData(
          color: AppColors.textPrimary,
          size: AppDimensions.iconSize,
        ),
        actionsIconTheme: const IconThemeData(
          color: AppColors.textSecondary,
          size: AppDimensions.iconSize,
        ),
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),

      // Card theme
      cardTheme: CardTheme(
        color: AppColors.emailCardBackground,
        elevation: AppDimensions.elevation2,
        shadowColor: Colors.black.withOpacity(0.1),
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: AppDimensions.borderRadiusMd,
        ),
        margin: AppDimensions.marginSm,
      ),

      // Elevated button theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.buttonPrimary,
          foregroundColor: AppColors.surface,
          elevation: AppDimensions.elevation2,
          shadowColor: Colors.black.withOpacity(0.1),
          surfaceTintColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: AppDimensions.borderRadiusSm,
          ),
          minimumSize: const Size(AppDimensions.buttonMinWidth, AppDimensions.buttonHeight),
          textStyle: AppTypography.button,
        ),
      ),

      // Text button theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.buttonPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: AppDimensions.borderRadiusSm,
          ),
          minimumSize: const Size(AppDimensions.buttonMinWidth, AppDimensions.buttonHeight),
          textStyle: AppTypography.button,
        ),
      ),

      // Outlined button theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.buttonPrimary,
          side: const BorderSide(color: AppColors.border),
          shape: RoundedRectangleBorder(
            borderRadius: AppDimensions.borderRadiusSm,
          ),
          minimumSize: const Size(AppDimensions.buttonMinWidth, AppDimensions.buttonHeight),
          textStyle: AppTypography.button,
        ),
      ),

      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.inputBackground,
        border: OutlineInputBorder(
          borderRadius: AppDimensions.borderRadiusSm,
          borderSide: const BorderSide(color: AppColors.inputBorder),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: AppDimensions.borderRadiusSm,
          borderSide: const BorderSide(color: AppColors.inputBorder),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: AppDimensions.borderRadiusSm,
          borderSide: const BorderSide(color: AppColors.inputBorderFocus, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: AppDimensions.borderRadiusSm,
          borderSide: const BorderSide(color: AppColors.inputBorderError),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: AppDimensions.borderRadiusSm,
          borderSide: const BorderSide(color: AppColors.inputBorderError, width: 2),
        ),
        contentPadding: AppDimensions.paddingMd,
        hintStyle: AppTypography.bodyMedium.copyWith(color: AppColors.inputPlaceholder),
        labelStyle: AppTypography.labelMedium,
        errorStyle: AppTypography.bodySmall.copyWith(color: AppColors.error),
      ),

      // Icon theme
      iconTheme: const IconThemeData(
        color: AppColors.textSecondary,
        size: AppDimensions.iconSize,
      ),

      // Primary icon theme
      primaryIconTheme: const IconThemeData(
        color: AppColors.surface,
        size: AppDimensions.iconSize,
      ),

      // List tile theme
      listTileTheme: ListTileThemeData(
        contentPadding: AppDimensions.paddingMd,
        shape: RoundedRectangleBorder(
          borderRadius: AppDimensions.borderRadiusSm,
        ),
        tileColor: AppColors.surface,
        selectedTileColor: AppColors.emailCardSelected,
        iconColor: AppColors.textSecondary,
        textColor: AppColors.textPrimary,
        titleTextStyle: AppTypography.bodyMedium,
        subtitleTextStyle: AppTypography.bodySmall.copyWith(color: AppColors.textSecondary),
      ),

      // Divider theme
      dividerTheme: const DividerThemeData(
        color: AppColors.border,
        thickness: 1,
        space: 1,
      ),

      // Chip theme
      chipTheme: ChipThemeData(
        backgroundColor: AppColors.periwinkleLighter,
        deleteIconColor: AppColors.textSecondary,
        disabledColor: AppColors.border,
        selectedColor: AppColors.periwinkleLight,
        secondarySelectedColor: AppColors.periwinkleBase,
        shadowColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
        labelStyle: AppTypography.labelMedium,
        secondaryLabelStyle: AppTypography.labelMedium.copyWith(color: AppColors.surface),
        brightness: Brightness.light,
        padding: AppDimensions.paddingSm,
        shape: RoundedRectangleBorder(
          borderRadius: AppDimensions.borderRadiusFull,
        ),
      ),

      // Bottom navigation bar theme
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: AppColors.surface,
        selectedItemColor: AppColors.periwinkleBase,
        unselectedItemColor: AppColors.textSecondary,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
        selectedLabelStyle: AppTypography.labelSmall,
        unselectedLabelStyle: AppTypography.labelSmall,
      ),

      // Navigation rail theme
      navigationRailTheme: NavigationRailThemeData(
        backgroundColor: AppColors.sidebarBackground,
        selectedIconTheme: const IconThemeData(
          color: AppColors.periwinkleBase,
          size: AppDimensions.iconSize,
        ),
        unselectedIconTheme: const IconThemeData(
          color: AppColors.textSecondary,
          size: AppDimensions.iconSize,
        ),
        selectedLabelTextStyle: AppTypography.labelMedium.copyWith(color: AppColors.periwinkleBase),
        unselectedLabelTextStyle: AppTypography.labelMedium.copyWith(color: AppColors.textSecondary),
        useIndicator: true,
        indicatorColor: AppColors.periwinkleLight,
      ),

      // Floating action button theme
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: AppColors.buttonPrimary,
        foregroundColor: AppColors.surface,
        elevation: AppDimensions.elevation6,
        shape: CircleBorder(),
      ),

      // Text theme
      textTheme: const TextTheme(
        displayLarge: AppTypography.displayLarge,
        displayMedium: AppTypography.displayMedium,
        displaySmall: AppTypography.displaySmall,
        headlineLarge: AppTypography.heading1,
        headlineMedium: AppTypography.heading2,
        headlineSmall: AppTypography.heading3,
        titleLarge: AppTypography.heading1,
        titleMedium: AppTypography.heading2,
        titleSmall: AppTypography.heading3,
        bodyLarge: AppTypography.bodyLarge,
        bodyMedium: AppTypography.bodyMedium,
        bodySmall: AppTypography.bodySmall,
        labelLarge: AppTypography.labelLarge,
        labelMedium: AppTypography.labelMedium,
        labelSmall: AppTypography.labelSmall,
      ),

      // Extensions
      extensions: const <ThemeExtension<dynamic>>[
        // Custom theme extensions can be added here
      ],
    );
  }

  /// Dark theme configuration
  static ThemeData get darkTheme {
    return lightTheme.copyWith(
      brightness: Brightness.dark,
      
      // Color scheme for dark mode
      colorScheme: const ColorScheme.dark(
        primary: AppColors.periwinkleDarkMode,
        onPrimary: AppColors.backgroundDark,
        primaryContainer: AppColors.periwinkleDark,
        onPrimaryContainer: AppColors.periwinkleLighter,
        
        secondary: AppColors.lavender,
        onSecondary: AppColors.backgroundDark,
        secondaryContainer: AppColors.surfaceDark,
        onSecondaryContainer: AppColors.textPrimaryDark,
        
        tertiary: AppColors.lilac,
        onTertiary: AppColors.backgroundDark,
        tertiaryContainer: AppColors.surfaceDark,
        onTertiaryContainer: AppColors.textPrimaryDark,
        
        error: AppColors.error,
        onError: AppColors.backgroundDark,
        errorContainer: Color(0xFF93000A),
        onErrorContainer: Color(0xFFFFDAD6),
        
        background: AppColors.backgroundDark,
        onBackground: AppColors.textPrimaryDark,
        
        surface: AppColors.surfaceDark,
        onSurface: AppColors.textPrimaryDark,
        surfaceVariant: AppColors.borderDark,
        onSurfaceVariant: AppColors.textSecondaryDark,
        
        outline: AppColors.borderDark,
        outlineVariant: AppColors.borderDark,
        
        shadow: Colors.black,
        scrim: Colors.black87,
        
        inverseSurface: AppColors.textPrimaryDark,
        onInverseSurface: AppColors.backgroundDark,
        inversePrimary: AppColors.periwinkleDark,
      ),

      scaffoldBackgroundColor: AppColors.backgroundDark,

      appBarTheme: lightTheme.appBarTheme?.copyWith(
        backgroundColor: AppColors.surfaceDark,
        foregroundColor: AppColors.textPrimaryDark,
        iconTheme: const IconThemeData(
          color: AppColors.textPrimaryDark,
          size: AppDimensions.iconSize,
        ),
        actionsIconTheme: const IconThemeData(
          color: AppColors.textSecondaryDark,
          size: AppDimensions.iconSize,
        ),
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),

      cardTheme: lightTheme.cardTheme?.copyWith(
        color: AppColors.surfaceDark,
      ),

      iconTheme: const IconThemeData(
        color: AppColors.textSecondaryDark,
        size: AppDimensions.iconSize,
      ),

      primaryIconTheme: const IconThemeData(
        color: AppColors.backgroundDark,
        size: AppDimensions.iconSize,
      ),

      textTheme: TextTheme(
        displayLarge: AppTypography.displayLargeDark,
        displayMedium: AppTypography.displayMediumDark,
        displaySmall: AppTypography.displaySmallDark,
        headlineLarge: AppTypography.heading1Dark,
        headlineMedium: AppTypography.heading2Dark,
        headlineSmall: AppTypography.heading3Dark,
        titleLarge: AppTypography.heading1Dark,
        titleMedium: AppTypography.heading2Dark,
        titleSmall: AppTypography.heading3Dark,
        bodyLarge: AppTypography.bodyLargeDark,
        bodyMedium: AppTypography.bodyMediumDark,
        bodySmall: AppTypography.bodySmallDark,
        labelLarge: AppTypography.labelLargeDark,
        labelMedium: AppTypography.labelMediumDark,
        labelSmall: AppTypography.labelSmallDark,
      ),
    );
  }
}
