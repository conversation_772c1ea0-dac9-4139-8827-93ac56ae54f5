class EnvConfig {
  // Gmail OAuth Configuration
  static const String gmailClientId = String.fromEnvironment(
    'GMAIL_CLIENT_ID',
    defaultValue: 'your_gmail_client_id_here',
  );
  
  static const String gmailClientSecret = String.fromEnvironment(
    'GMAIL_CLIENT_SECRET', 
    defaultValue: 'your_gmail_client_secret_here',
  );
  
  // Web Client ID for iOS Google Sign In
  static const String webClientId = String.fromEnvironment(
    'WEB_CLIENT_ID',
    defaultValue: 'your_web_client_id_here',
  );
  
  // Firebase Configuration
  static const String firebaseApiKey = String.fromEnvironment(
    'FIREBASE_API_KEY',
    defaultValue: 'your_firebase_api_key_here',
  );
  
  // AI Services
  static const String geminiApiKey = String.fromEnvironment(
    'GEMINI_API_KEY',
    defaultValue: 'your_gemini_api_key_here',
  );
  
  // Environment
  static const String environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );
  
  static bool get isDevelopment => environment == 'development';
  static bool get isProduction => environment == 'production';
}