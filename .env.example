# Environment Configuration Template
# Copy this file to .env and fill in your actual values

# Environment
ENVIRONMENT=development

# Gmail API Configuration
GMAIL_CLIENT_ID=your_gmail_client_id_here
GMAIL_CLIENT_SECRET=your_gmail_client_secret_here

# AI Services
GEMINI_API_KEY=your_gemini_api_key_here

# Firebase Configuration
FIREBASE_API_KEY=your_firebase_api_key_here
FIREBASE_PROJECT_ID=your_firebase_project_id_here
FIREBASE_MESSAGING_SENDER_ID=your_firebase_messaging_sender_id_here
FIREBASE_APP_ID=your_firebase_app_id_here

# API Configuration
API_BASE_URL=https://api.gmail.com
CONNECTION_TIMEOUT=30000
RECEIVE_TIMEOUT=30000

# Feature Flags
ENABLE_AI_FEATURES=true
ENABLE_VOICE_ASSISTANT=false
ENABLE_ANALYTICS=true
ENABLE_CRASH_REPORTING=true

# Debug Settings
DEBUG_MODE=true
VERBOSE_LOGGING=true