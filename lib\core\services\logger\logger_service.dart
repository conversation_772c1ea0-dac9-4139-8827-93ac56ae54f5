import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';

/// Enhanced Logger Service with file logging and configuration options
class LoggerService extends GetxService {
  static LoggerService get instance => Get.find<LoggerService>();

  late Logger _logger;
  late Logger _fileLogger;
  bool _isInitialized = false;

  // Log levels
  static const Level verboseLevel = Level.trace;
  static const Level debugLevel = Level.debug;
  static const Level infoLevel = Level.info;
  static const Level warningLevel = Level.warning;
  static const Level errorLevel = Level.error;
  static const Level fatalLevel = Level.fatal;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeLogger();
  }

  Future<void> _initializeLogger() async {
    try {
      // Console logger for development
      _logger = Logger(
        level: _getLogLevel(),
        printer: PrettyPrinter(
          methodCount: 2,
          errorMethodCount: 8,
          lineLength: 120,
          dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
          excludeBox: {
            Level.trace: true,
            Level.debug: true,
          },
        ),
        filter: ProductionFilter(),
      );

      // File logger for production
      await _initializeFileLogger();

      _isInitialized = true;
      info('LoggerService initialized successfully');
    } catch (e) {
      print('Failed to initialize LoggerService: $e');
    }
  }

  Future<void> _initializeFileLogger() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logFile = File('${directory.path}/app_logs.txt');

      _fileLogger = Logger(
        level: Level.warning, // Only log warnings and above to file
        printer: SimplePrinter(colors: false),
        output: FileOutput(file: logFile),
        filter: ProductionFilter(),
      );
    } catch (e) {
      print('Failed to initialize file logger: $e');
      // Fallback to console only
      _fileLogger = _logger;
    }
  }

  Level _getLogLevel() {
    // In debug mode, show all logs
    if (kDebugMode) {
      return Level.trace;
    }
    // In production, only show info and above
    return Level.info;
  }

  // Static methods for easy access
  static void verbose(String message, [String? tag]) {
    if (Get.isRegistered<LoggerService>()) {
      instance._log(verboseLevel, message, tag: tag);
    } else {
      print('[VERBOSE]${tag != null ? '[$tag]' : ''} $message');
    }
  }

  static void debug(String message, [String? tag]) {
    if (Get.isRegistered<LoggerService>()) {
      instance._log(debugLevel, message, tag: tag);
    } else {
      print('[DEBUG]${tag != null ? '[$tag]' : ''} $message');
    }
  }

  static void info(String message, [String? tag]) {
    if (Get.isRegistered<LoggerService>()) {
      instance._log(infoLevel, message, tag: tag);
    } else {
      print('[INFO]${tag != null ? '[$tag]' : ''} $message');
    }
  }

  static void warning(String message, [String? tag, dynamic error]) {
    if (Get.isRegistered<LoggerService>()) {
      instance._log(warningLevel, message, tag: tag, error: error);
    } else {
      print('[WARNING]${tag != null ? '[$tag]' : ''} $message${error != null ? ' - Error: $error' : ''}');
    }
  }

  static void error(String message, [dynamic error, StackTrace? stackTrace, String? tag]) {
    if (Get.isRegistered<LoggerService>()) {
      instance._log(errorLevel, message, tag: tag, error: error, stackTrace: stackTrace);
    } else {
      print('[ERROR]${tag != null ? '[$tag]' : ''} $message${error != null ? ' - Error: $error' : ''}');
      if (stackTrace != null) print('StackTrace: $stackTrace');
    }
  }

  static void fatal(String message, [dynamic error, StackTrace? stackTrace, String? tag]) {
    if (Get.isRegistered<LoggerService>()) {
      instance._log(fatalLevel, message, tag: tag, error: error, stackTrace: stackTrace);
    } else {
      print('[FATAL]${tag != null ? '[$tag]' : ''} $message${error != null ? ' - Error: $error' : ''}');
      if (stackTrace != null) print('StackTrace: $stackTrace');
    }
  }

  // Instance method for actual logging
  void _log(Level level, String message, {String? tag, dynamic error, StackTrace? stackTrace}) {
    if (!_isInitialized) {
      print('[$level]${tag != null ? '[$tag]' : ''} $message');
      return;
    }

    final formattedMessage = tag != null ? '[$tag] $message' : message;

    // Log to console
    switch (level) {
      case Level.trace:
        _logger.t(formattedMessage, error: error, stackTrace: stackTrace);
        break;
      case Level.debug:
        _logger.d(formattedMessage, error: error, stackTrace: stackTrace);
        break;
      case Level.info:
        _logger.i(formattedMessage, error: error, stackTrace: stackTrace);
        break;
      case Level.warning:
        _logger.w(formattedMessage, error: error, stackTrace: stackTrace);
        break;
      case Level.error:
        _logger.e(formattedMessage, error: error, stackTrace: stackTrace);
        break;
      case Level.fatal:
        _logger.f(formattedMessage, error: error, stackTrace: stackTrace);
        break;
      default:
        _logger.i(formattedMessage, error: error, stackTrace: stackTrace);
    }

    // Log to file for warnings and above
    if (level.index >= Level.warning.index) {
      try {
        _fileLogger.log(level, formattedMessage, error: error, stackTrace: stackTrace);
      } catch (e) {
        print('Failed to log to file: $e');
      }
    }
  }
}

/// Custom file output for logger
class FileOutput extends LogOutput {
  final File file;

  FileOutput({required this.file});

  @override
  void output(OutputEvent event) {
    try {
      final timestamp = DateTime.now().toIso8601String();
      final logEntry = '[$timestamp] ${event.lines.join('\n')}\n';
      file.writeAsStringSync(logEntry, mode: FileMode.append);
    } catch (e) {
      print('Failed to write to log file: $e');
    }
  }
}