# Folder Structure Documentation
## AI-Powered Email Client

---

## Complete Project Structure

```plaintext
ai_email_client/
├── android/                          # Android platform-specific code
│   ├── app/
│   │   ├── src/
│   │   │   └── main/
│   │   │       ├── kotlin/          # Kotlin code for Android
│   │   │       ├── AndroidManifest.xml
│   │   │       └── res/             # Android resources
│   │   └── build.gradle.kts
│   ├── gradle/
│   ├── build.gradle.kts
│   └── settings.gradle
│
├── ios/                              # iOS platform-specific code
│   ├── Runner/
│   │   ├── AppDelegate.swift
│   │   ├── Info.plist
│   │   └── Assets.xcassets/
│   ├── Runner.xcodeproj/
│   └── Runner.xcworkspace/
│
├── web/                              # Web platform files
│   ├── index.html
│   ├── manifest.json
│   ├── favicon.png
│   └── icons/
│
├── lib/                              # Main Flutter application code
│   ├── main.dart                     # Application entry point
│   │
│   ├── core/                         # Core application layer
│   │   ├── app/                      # App-wide configurations
│   │   │   ├── app.dart              # Main app widget
│   │   │   ├── bindings/
│   │   │   │   └── initial_binding.dart
│   │   │   ├── routes/
│   │   │   │   ├── app_pages.dart    # Route definitions
│   │   │   │   └── app_routes.dart   # Route names
│   │   │   ├── themes/
│   │   │   │   ├── app_theme.dart    # Theme configuration
│   │   │   │   ├── light_theme.dart
│   │   │   │   ├── dark_theme.dart
│   │   │   │   └── theme_controller.dart
│   │   │   └── translations/
│   │   │       ├── app_translations.dart
│   │   │       ├── en_us.dart
│   │   │       ├── es_es.dart
│   │   │       └── fr_fr.dart
│   │   │
│   │   ├── constants/                # Global constants
│   │   │   ├── api_constants.dart
│   │   │   ├── app_constants.dart
│   │   │   ├── storage_constants.dart
│   │   │   └── ui_constants.dart
│   │   │
│   │   ├── models/                   # Shared data models
│   │   │   ├── user_model.dart
│   │   │   ├── email_model.dart
│   │   │   ├── attachment_model.dart
│   │   │   └── label_model.dart
│   │   │
│   │   ├── services/                 # Core services
│   │   │   ├── api/
│   │   │   │   ├── api_service.dart
│   │   │   │   ├── dio_client.dart
│   │   │   │   └── interceptors/
│   │   │   │       ├── auth_interceptor.dart
│   │   │   │       ├── logging_interceptor.dart
│   │   │   │       └── error_interceptor.dart
│   │   │   ├── storage/
│   │   │   │   ├── local_storage_service.dart
│   │   │   │   ├── secure_storage_service.dart
│   │   │   │   └── database_service.dart
│   │   │   ├── auth/
│   │   │   │   ├── auth_service.dart
│   │   │   │   └── oauth_service.dart
│   │   │   ├── notification/
│   │   │   │   ├── notification_service.dart
│   │   │   │   ├── local_notification_service.dart
│   │   │   │   └── push_notification_service.dart
│   │   │   ├── connectivity/
│   │   │   │   └── connectivity_service.dart
│   │   │   └── logger/
│   │   │       └── logger_service.dart
│   │   │
│   │   ├── utils/                    # Utility functions
│   │   │   ├── date_utils.dart
│   │   │   ├── string_utils.dart
│   │   │   ├── validation_utils.dart
│   │   │   ├── file_utils.dart
│   │   │   ├── email_utils.dart
│   │   │   └── encryption_utils.dart
│   │   │
│   │   └── widgets/                  # Reusable UI components
│   │       ├── buttons/
│   │       │   ├── primary_button.dart
│   │       │   ├── secondary_button.dart
│   │       │   └── icon_button_widget.dart
│   │       ├── cards/
│   │       │   ├── email_card.dart
│   │       │   ├── event_card.dart
│   │       │   └── stat_card.dart
│   │       ├── inputs/
│   │       │   ├── text_field_widget.dart
│   │       │   ├── search_bar_widget.dart
│   │       │   └── rich_text_editor.dart
│   │       ├── dialogs/
│   │       │   ├── confirmation_dialog.dart
│   │       │   ├── loading_dialog.dart
│   │       │   └── error_dialog.dart
│   │       ├── loaders/
│   │       │   ├── skeleton_loader.dart
│   │       │   ├── spinner_widget.dart
│   │       │   └── progress_bar.dart
│   │       ├── navigation/
│   │       │   ├── bottom_nav_bar.dart
│   │       │   ├── app_drawer.dart
│   │       │   └── top_app_bar.dart
│   │       ├── lists/
│   │       │   ├── email_list_item.dart
│   │       │   ├── attachment_list_item.dart
│   │       │   └── event_list_item.dart
│   │       └── common/
│   │           ├── avatar_widget.dart
│   │           ├── badge_widget.dart
│   │           ├── chip_widget.dart
│   │           ├── divider_widget.dart
│   │           └── empty_state.dart
│   │
│   ├── data/                         # Data layer
│   │   ├── models/                   # Data transfer objects
│   │   │   ├── dto/
│   │   │   │   ├── email_dto.dart
│   │   │   │   ├── user_dto.dart
│   │   │   │   ├── attachment_dto.dart
│   │   │   │   └── label_dto.dart
│   │   │   └── response/
│   │   │       ├── api_response.dart
│   │   │       ├── gmail_response.dart
│   │   │       └── ai_response.dart
│   │   │
│   │   └── repositories/             # Data access layer
│   │       ├── email_repository.dart
│   │       ├── user_repository.dart
│   │       ├── attachment_repository.dart
│   │       ├── label_repository.dart
│   │       ├── ai_insight_repository.dart
│   │       └── calendar_repository.dart
│   │
│   └── features/                     # Feature modules
│       │
│       ├── authentication/           # Authentication feature
│       │   ├── bindings/
│       │   │   └── auth_binding.dart
│       │   ├── controllers/
│       │   │   ├── login_controller.dart
│       │   │   └── oauth_controller.dart
│       │   ├── models/
│       │   │   └── auth_state.dart
│       │   ├── presentation/
│       │   │   ├── screens/
│       │   │   │   ├── login_screen.dart
│       │   │   │   ├── oauth_screen.dart
│       │   │   │   └── splash_screen.dart
│       │   │   └── widgets/
│       │   │       ├── login_form.dart
│       │   │       └── oauth_button.dart
│       │   └── services/
│       │       └── auth_feature_service.dart
│       │
│       ├── onboarding/               # Onboarding feature
│       │   ├── bindings/
│       │   │   └── onboarding_binding.dart
│       │   ├── controllers/
│       │   │   ├── onboarding_controller.dart
│       │   │   └── tutorial_controller.dart
│       │   ├── presentation/
│       │   │   ├── screens/
│       │   │   │   ├── welcome_screen.dart
│       │   │   │   ├── tutorial_screen.dart
│       │   │   │   └── initial_sync_screen.dart
│       │   │   └── widgets/
│       │   │       ├── onboarding_page.dart
│       │   │       └── progress_indicator.dart
│       │   └── models/
│       │       └── onboarding_page_model.dart
│       │
│       ├── dashboard/                # Dashboard feature
│       │   ├── bindings/
│       │   │   └── dashboard_binding.dart
│       │   ├── controllers/
│       │   │   ├── dashboard_controller.dart
│       │   │   └── quick_stats_controller.dart
│       │   ├── models/
│       │   │   ├── dashboard_data.dart
│       │   │   └── quick_stat.dart
│       │   ├── presentation/
│       │   │   ├── screens/
│       │   │   │   └── dashboard_screen.dart
│       │   │   └── widgets/
│       │   │       ├── focus_section.dart
│       │   │       ├── timeline_widget.dart
│       │   │       ├── ai_digest_card.dart
│       │   │       ├── quick_stats_row.dart
│       │   │       └── category_section.dart
│       │   └── services/
│       │       └── dashboard_service.dart
│       │
│       ├── inbox/                    # Inbox feature
│       │   ├── bindings/
│       │   │   └── inbox_binding.dart
│       │   ├── controllers/
│       │   │   ├── inbox_controller.dart
│       │   │   ├── email_list_controller.dart
│       │   │   └── email_filter_controller.dart
│       │   ├── models/
│       │   │   ├── email_list_state.dart
│       │   │   ├── filter_options.dart
│       │   │   └── sort_options.dart
│       │   ├── presentation/
│       │   │   ├── screens/
│       │   │   │   └── inbox_screen.dart
│       │   │   └── widgets/
│       │   │       ├── email_list.dart
│       │   │       ├── email_list_item.dart
│       │   │       ├── filter_chips.dart
│       │   │       ├── sort_menu.dart
│       │   │       └── selection_toolbar.dart
│       │   └── services/
│       │       └── email_sync_service.dart
│       │
│       ├── email_detail/             # Email detail feature
│       │   ├── bindings/
│       │   │   └── email_detail_binding.dart
│       │   ├── controllers/
│       │   │   ├── email_detail_controller.dart
│       │   │   ├── email_actions_controller.dart
│       │   │   └── attachment_controller.dart
│       │   ├── models/
│       │   │   ├── email_detail_state.dart
│       │   │   └── email_thread.dart
│       │   ├── presentation/
│       │   │   ├── screens/
│       │   │   │   └── email_detail_screen.dart
│       │   │   └── widgets/
│       │   │       ├── email_header.dart
│       │   │       ├── sender_info_card.dart
│       │   │       ├── email_body.dart
│       │   │       ├── ai_insights_panel.dart
│       │   │       ├── attachment_section.dart
│       │   │       ├── smart_replies_sheet.dart
│       │   │       └── related_emails.dart
│       │   └── services/
│       │       └── email_detail_service.dart
│       │
│       ├── compose/                  # Email composition feature
│       │   ├── bindings/
│       │   │   └── compose_binding.dart
│       │   ├── controllers/
│       │   │   ├── compose_controller.dart
│       │   │   ├── recipients_controller.dart
│       │   │   └── attachment_picker_controller.dart
│       │   ├── models/
│       │   │   ├── draft_model.dart
│       │   │   └── compose_state.dart
│       │   ├── presentation/
│       │   │   ├── screens/
│       │   │   │   └── compose_screen.dart
│       │   │   └── widgets/
│       │   │       ├── recipients_field.dart
│       │   │       ├── subject_field.dart
│       │   │       ├── rich_editor.dart
│       │   │       ├── compose_toolbar.dart
│       │   │       ├── attachment_picker.dart
│       │   │       └── send_button.dart
│       │   └── services/
│       │       ├── compose_service.dart
│       │       └── draft_service.dart
│       │
│       ├── calendar/                 # Calendar feature
│       │   ├── bindings/
│       │   │   └── calendar_binding.dart
│       │   ├── controllers/
│       │   │   ├── calendar_controller.dart
│       │   │   ├── event_detail_controller.dart
│       │   │   └── reminder_controller.dart
│       │   ├── models/
│       │   │   ├── calendar_event.dart
│       │   │   ├── calendar_view_mode.dart
│       │   │   └── reminder_model.dart
│       │   ├── presentation/
│       │   │   ├── screens/
│       │   │   │   ├── calendar_screen.dart
│       │   │   │   └── event_detail_screen.dart
│       │   │   └── widgets/
│       │   │       ├── calendar_component.dart
│       │   │       ├── day_view.dart
│       │   │       ├── week_view.dart
│       │   │       ├── month_view.dart
│       │   │       ├── agenda_view.dart
│       │   │       ├── event_card.dart
│       │   │       └── view_selector.dart
│       │   └── services/
│       │       ├── calendar_sync_service.dart
│       │       └── event_extraction_service.dart
│       │
│       ├── search/                   # Search feature
│       │   ├── bindings/
│       │   │   └── search_binding.dart
│       │   ├── controllers/
│       │   │   ├── search_controller.dart
│       │   │   ├── search_filter_controller.dart
│       │   │   └── natural_language_controller.dart
│       │   ├── models/
│       │   │   ├── search_query.dart
│       │   │   ├── search_result.dart
│       │   │   └── search_suggestion.dart
│       │   ├── presentation/
│       │   │   ├── screens/
│       │   │   │   └── search_screen.dart
│       │   │   └── widgets/
│       │   │       ├── search_bar.dart
│       │   │       ├── search_suggestions.dart
│       │   │       ├── filter_panel.dart
│       │   │       ├── search_results.dart
│       │   │       └── natural_language_examples.dart
│       │   └── services/
│       │       ├── search_service.dart
│       │       └── indexing_service.dart
│       │
│       ├── attachments/              # Attachment hub feature
│       │   ├── bindings/
│       │   │   └── attachments_binding.dart
│       │   ├── controllers/
│       │   │   ├── attachments_controller.dart
│       │   │   ├── attachment_preview_controller.dart
│       │   │   └── download_controller.dart
│       │   ├── models/
│       │   │   ├── attachment_filter.dart
│       │   │   └── attachment_view_mode.dart
│       │   ├── presentation/
│       │   │   ├── screens/
│       │   │   │   ├── attachments_screen.dart
│       │   │   │   └── attachment_preview_screen.dart
│       │   │   └── widgets/
│       │   │       ├── attachment_grid.dart
│       │   │       ├── attachment_list.dart
│       │   │       ├── attachment_card.dart
│       │   │       ├── file_type_filter.dart
│       │   │       └── preview_modal.dart
│       │   └── services/
│       │       ├── attachment_download_service.dart
│       │       └── attachment_manager_service.dart
│       │
│       ├── ai_insights/              # AI insights feature
│       │   ├── bindings/
│       │   │   └── ai_insights_binding.dart
│       │   ├── controllers/
│       │   │   ├── ai_insights_controller.dart
│       │   │   ├── smart_reply_controller.dart
│       │   │   └── summary_controller.dart
│       │   ├── models/
│       │   │   ├── insight_model.dart
│       │   │   ├── smart_reply_model.dart
│       │   │   ├── summary_model.dart
│       │   │   ├── extracted_entity.dart
│       │   │   └── priority_score.dart
│       │   ├── presentation/
│       │   │   ├── screens/
│       │   │   │   └── insights_overview_screen.dart
│       │   │   └── widgets/
│       │   │       ├── summary_card.dart
│       │   │       ├── extracted_info_card.dart
│       │   │       ├── smart_reply_suggestions.dart
│       │   │       ├── related_emails_section.dart
│       │   │       └── priority_badge.dart
│       │   └── services/
│       │       ├── ai_processing_service.dart
│       │       ├── classification_service.dart
│       │       ├── entity_extraction_service.dart
│       │       ├── summarization_service.dart
│       │       ├── smart_reply_service.dart
│       │       └── priority_scoring_service.dart
│       │
│       ├── settings/                 # Settings feature
│       │   ├── bindings/
│       │   │   └── settings_binding.dart
│       │   ├── controllers/
│       │   │   ├── settings_controller.dart
│       │   │   ├── account_controller.dart
│       │   │   ├── appearance_controller.dart
│       │   │   ├── notification_settings_controller.dart
│       │   │   └── privacy_controller.dart
│       │   ├── models/
│       │   │   ├── settings_model.dart
│       │   │   ├── notification_preferences.dart
│       │   │   └── privacy_settings.dart
│       │   ├── presentation/
│       │   │   ├── screens/
│       │   │   │   ├── settings_screen.dart
│       │   │   │   ├── account_settings_screen.dart
│       │   │   │   ├── appearance_settings_screen.dart
│       │   │   │   ├── notification_settings_screen.dart
│       │   │   │   ├── privacy_settings_screen.dart
│       │   │   │   └── about_screen.dart
│       │   │   └── widgets/
│       │   │       ├── settings_section.dart
│       │   │       ├── settings_tile.dart
│       │   │       ├── toggle_setting.dart
│       │   │       └── profile_header.dart
│       │   └── services/
│       │       └── settings_service.dart
│       │
│       ├── notifications/            # Notifications feature
│       │   ├── bindings/
│       │   │   └── notifications_binding.dart
│       │   ├── controllers/
│       │   │   ├── notifications_controller.dart
│       │   │   └── notification_center_controller.dart
│       │   ├── models/
│       │   │   ├── notification_model.dart
│       │   │   └── notification_type.dart
│       │   ├── presentation/
│       │   │   ├── screens/
│       │   │   │   └── notification_center_screen.dart
│       │   │   └── widgets/
│       │   │       ├── notification_card.dart
│       │   │       └── notification_list.dart
│       │   └── services/
│       │       ├── notification_manager_service.dart
│       │       └── reminder_service.dart
│       │
│       ├── analytics/                # Analytics feature
│       │   ├── bindings/
│       │   │   └── analytics_binding.dart
│       │   ├── controllers/
│       │   │   └── analytics_controller.dart
│       │   ├── models/
│       │   │   ├── analytics_data.dart
│       │   │   └── chart_data.dart
│       │   ├── presentation/
│       │   │   ├── screens/
│       │   │   │   └── analytics_screen.dart
│       │   │   └── widgets/
│       │   │       ├── email_volume_chart.dart
│       │   │       ├── category_distribution.dart
│       │   │       ├── response_time_chart.dart
│       │   │       └── trend_graph.dart
│       │   └── services/
│       │       └── analytics_service.dart
│       │
│       └── voice_assistant/         # Voice assistant feature (Bonus)
│           ├── bindings/
│           │   └── voice_binding.dart
│           ├── controllers/
│           │   └── voice_controller.dart
│           ├── models/
│           │   ├── voice_command.dart
│           │   └── voice_response.dart
│           ├── presentation/
│           │   ├── screens/
│           │   │   └── voice_interface_screen.dart
│           │   └── widgets/
│           │       ├── voice_button.dart
│           │       ├── voice_animation.dart
│           │       └── command_list.dart
│           └── services/
│               ├── speech_recognition_service.dart
│               └── text_to_speech_service.dart
│
├── assets/                           # Static assets
│   ├── images/
│   │   ├── logo.png
│   │   ├── logo_dark.png
│   │   ├── splash_background.png
│   │   ├── onboarding/
│   │   │   ├── welcome.png
│   │   │   ├── feature_1.png
│   │   │   ├── feature_2.png
│   │   │   └── feature_3.png
│   │   ├── illustrations/
│   │   │   ├── empty_inbox.svg
│   │   │   ├── no_results.svg
│   │   │   ├── error.svg
│   │   │   └── success.svg
│   │   └── icons/
│   │       ├── app_icon.png
│   │       └── notification_icon.png
│   │
│   ├── fonts/
│   │   ├── Inter-Regular.ttf
│   │   ├── Inter-Medium.ttf
│   │   ├── Inter-SemiBold.ttf
│   │   ├── Inter-Bold.ttf
│   │   └── JetBrainsMono-Regular.ttf
│   │
│   ├── animations/
│   │   ├── loading.json
│   │   ├── success.json
│   │   └── ai_processing.json
│   │
│   └── translations/
│       ├── en.json
│       ├── es.json
│       ├── fr.json
│       └── de.json
│
├── test/                             # Test files
│   ├── unit/
│   │   ├── core/
│   │   │   ├── services/
│   │   │   │   ├── api_service_test.dart
│   │   │   │   └── storage_service_test.dart
│   │   │   └── utils/
│   │   │       ├── date_utils_test.dart
│   │   │       └── validation_utils_test.dart
│   │   ├── data/
│   │   │   └── repositories/
│   │   │       ├── email_repository_test.dart
│   │   │       └── user_repository_test.dart
│   │   └── features/
│   │       ├── authentication/
│   │       │   └── controllers/
│   │       │       └── login_controller_test.dart
│   │       └── inbox/
│   │           └── controllers/
│   │               └── inbox_controller_test.dart
│   │
│   ├── widget/
│   │   ├── core/
│   │   │   └── widgets/
│   │   │       ├── button_test.dart
│   │   │       └── card_test.dart
│   │   └── features/
│   │       ├── authentication/
│   │       │   └── login_screen_test.dart
│   │       └── inbox/
│   │           └── inbox_screen_test.dart
│   │
│   ├── integration/
│   │   ├── authentication_flow_test.dart
│   │   ├── email_management_test.dart
│   │   ├── calendar_integration_test.dart
│   │   └── search_functionality_test.dart
│   │
│   └── mocks/
│       ├── mock_api_service.dart
│       ├── mock_storage_service.dart
│       ├── mock_email_repository.dart
│       └── mock_ai_service.dart
│
├── docs/                             # Documentation
│   ├── requirements_documentation.md
│   ├── design.md
│   ├── tech_stack.md
│   ├── folder_structure.md
│   ├── api/
│   │   ├── gmail_api.md
│   │   └── internal_api.md
│   ├── architecture/
│   │   ├── overview.md
│   │   ├── data_flow.md
│   │   └── state_management.md
│   ├── features/
│   │   ├── authentication.md
│   │   ├── ai_insights.md
│   │   └── calendar.md
│   └── guides/
│       ├── setup.md
│       ├── development.md
│       ├── testing.md
│       └── deployment.md
│
├── scripts/                          # Utility scripts
│   ├── setup.sh
│   ├── build.sh
│   ├── test.sh
│   └── deploy.sh
│
├── .github/                          # GitHub configuration
│   ├── workflows/
│   │   ├── ci.yml
│   │   ├── cd.yml
│   │   ├── lint.yml
│   │   └── test.yml
│   ├── ISSUE_TEMPLATE/
│   │   ├── bug_report.md
│   │   └── feature_request.md
│   └── pull_request_template.md
│
├── .vscode/                          # VS Code configuration
│   ├── launch.json
│   ├── settings.json
│   └── extensions.json
│
├── .idea/                            # Android Studio configuration
│   └── runConfigurations/
│
├── pubspec.yaml                      # Flutter dependencies
├── pubspec.lock
├── analysis_options.yaml             # Dart linter configuration
├── .gitignore
├── .env.example                      # Environment variables template
├── .env.dev                          # Development environment
├── .env.staging                      # Staging environment
├── .env.prod                         # Production environment
├── README.md                         # Project documentation
├── CHANGELOG.md                      # Version history
├── LICENSE                           # License file
└── tasksheet.todo                    # Task tracking
```

---

## Detailed Folder Explanations

### 1. **lib/core/** - Core Application Layer
**Purpose**: Contains all shared resources, configurations, and utilities used across the entire application.

#### **core/app/**
- Application-wide setup and configuration
- Route management
- Theme definitions
- Internationalization

#### **core/constants/**
- Immutable values used throughout the app
- API endpoints, storage keys, UI constants
- Centralized for easy maintenance

#### **core/models/**
- Base models shared across features
- Data entities used in multiple modules

#### **core/services/**
- Singleton services for core functionality
- API communication, storage, authentication
- Independent of specific features

#### **core/utils/**
- Helper functions and extensions
- Reusable logic without state

#### **core/widgets/**
- Reusable UI components
- Design system building blocks
- Consistent across all features

---

### 2. **lib/data/** - Data Layer
**Purpose**: Handles data operations and abstracts data sources.

#### **data/models/**
- DTOs (Data Transfer Objects)
- API response models
- Mapping between API and domain models

#### **data/repositories/**
- Abstract data access
- Combines multiple data sources
- Business logic for data operations

---

### 3. **lib/features/** - Feature Modules
**Purpose**: Feature-based organization for scalability and modularity.

#### **Feature Structure** (Each feature follows this pattern):
```
feature_name/
├── bindings/          # GetX dependency injection
├── controllers/       # Business logic and state management
├── models/            # Feature-specific data models
├── presentation/      # UI layer
│   ├── screens/       # Full-screen pages
│   └── widgets/       # Feature-specific components
└── services/          # Feature-specific business services
```

#### **Key Features**:
- **authentication**: Login, OAuth, session management
- **onboarding**: Welcome, tutorial, initial setup
- **dashboard**: Main overview, AI digest, quick stats
- **inbox**: Email list, filters, batch operations
- **email_detail**: Single email view, actions, insights
- **compose**: Email creation, drafts, attachments
- **calendar**: Events, reminders, timeline
- **search**: Natural language search, filters
- **attachments**: File management, previews
- **ai_insights**: AI processing, summaries, smart replies
- **settings**: App configuration, preferences
- **notifications**: Alerts, reminders
- **analytics**: Usage statistics, visualizations
- **voice_assistant**: Voice commands (bonus feature)

---

### 4. **assets/** - Static Assets
**Purpose**: Images, fonts, animations, and translations.

#### **Subfolders**:
- **images/**: PNG, JPG, SVG files
- **fonts/**: Custom typefaces
- **animations/**: Lottie JSON files
- **translations/**: JSON locale files

---

### 5. **test/** - Testing Suite
**Purpose**: Comprehensive testing coverage.

#### **Test Types**:
- **unit/**: Test individual functions and classes
- **widget/**: Test UI components
- **integration/**: Test complete user flows
- **mocks/**: Mock objects for testing

---

### 6. **docs/** - Documentation
**Purpose**: Project documentation for developers and users.

#### **Sections**:
- Requirements and specifications
- Architecture diagrams
- API documentation
- Feature guides
- Setup and deployment instructions

---

### 7. **scripts/** - Automation Scripts
**Purpose**: Simplify common development tasks.

- **setup.sh**: Initial project setup
- **build.sh**: Build app for all platforms
- **test.sh**: Run all tests
- **deploy.sh**: Deploy to stores

---

### 8. **.github/** - GitHub Integration
**Purpose**: CI/CD pipelines and GitHub templates.

- **workflows/**: GitHub Actions
- **ISSUE_TEMPLATE/**: Bug and feature templates
- **pull_request_template.md**: PR guidelines

---

## File Naming Conventions

### Dart Files
```
snake_case.dart          # All Dart files
feature_screen.dart      # Screens
feature_controller.dart  # Controllers
feature_service.dart     # Services
feature_model.dart       # Models
feature_widget.dart      # Widgets
feature_binding.dart     # Bindings
feature_repository.dart  # Repositories
```

### Asset Files
```
lowercase_with_underscores.png
feature_illustration.svg
icon_name_24dp.png
```

### Test Files
```
file_name_test.dart      # Test files mirror source structure
```

---

## Import Conventions

### Import Order
```dart
// 1. Dart SDK imports
import 'dart:async';
import 'dart:io';

// 2. Flutter framework imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// 3. Third-party package imports
import 'package:get/get.dart';
import 'package:dio/dio.dart';

// 4. Project imports
import 'package:ai_email_client/core/constants/app_constants.dart';
import 'package:ai_email_client/core/services/api_service.dart';

// 5. Relative imports (if necessary)
import '../widgets/custom_button.dart';
```

---

## Module Independence

### Feature Isolation Rules
1. ✅ Features can import from **core/**
2. ✅ Features can import from **data/**
3. ❌ Features should NOT import from other features
4. ✅ Use shared models in **core/models/** for cross-feature communication
5. ✅ Use events/streams for inter-feature communication

---

## Scalability Considerations

### Adding New Features
```
1. Create feature folder under lib/features/
2. Follow standard feature structure (bindings, controllers, models, presentation, services)
3. Register routes in core/app/routes/
4. Add dependency injection in feature binding
5. Update navigation if needed
```

### Adding New Services
```
1. Create service file in core/services/
2. Implement as singleton if app-wide
3. Inject in InitialBinding
4. Document service API
```

### Adding New Models
```
1. Shared models → core/models/
2. Feature-specific models → feature/models/
3. DTOs → data/models/dto/
4. Include fromJson/toJson methods
```

---

## Best Practices

### Organization
- ✅ Keep files small and focused (< 300 lines)
- ✅ One class per file
- ✅ Group related files in folders
- ✅ Use meaningful names

### Dependencies
- ✅ Inject dependencies via GetX bindings
- ✅ Use interfaces for testability
- ✅ Avoid circular dependencies

### State Management
- ✅ Controllers handle business logic
- ✅ Presentation layer is thin (UI only)
- ✅ Use reactive programming (Rx)

### Testing
- ✅ Mirror source structure in tests
- ✅ Mock external dependencies
- ✅ Test at all levels (unit, widget, integration)

---

## Summary

This folder structure provides:
- **Scalability**: Easy to add new features
- **Maintainability**: Clear separation of concerns
- **Testability**: Isolated components
- **Reusability**: Shared core resources
- **Collaboration**: Multiple developers can work independently
- **Performance**: Lazy loading of features