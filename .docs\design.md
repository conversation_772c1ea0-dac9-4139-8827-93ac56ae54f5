# Design Documentation
## AI-Powered Email Client - UI/UX Design Specification

---

## 1. Design Philosophy

### Core Principles
- **Clarity Over Complexity**: Simple, intuitive interfaces that don't overwhelm
- **Intelligence, Not Intrusion**: AI enhances without being pushy
- **Calm Technology**: Reduce notification anxiety, increase focus
- **Accessibility First**: Usable by everyone, regardless of ability
- **Performance is a Feature**: Fast, smooth, responsive at all times

### Design Goals
- Create a peaceful, focused email experience
- Surface important information proactively
- Minimize cognitive load through smart organization
- Make AI insights actionable and trustworthy
- Maintain visual consistency across platforms

---

## 2. Visual Design System

### 2.1 Color Palette (Periwinkle Theme)

#### Primary Colors
```
Periwinkle Base:     #CCCCFF  (Primary brand color)
Periwinkle Dark:     #9999CC  (Headers, emphasis)
Periwinkle Darker:   #666699  (Text, icons)
Periwinkle Light:    #E6E6FF  (Backgrounds, cards)
Periwinkle Lighter:  #F5F5FF  (Subtle backgrounds)
```

#### Secondary Colors
```
Lavender:            #E6E6FA  (Accents, highlights)
Lilac:               #C8A2C8  (Secondary actions)
Slate Blue:          #6A5ACD  (CTAs, buttons)
Royal Blue:          #4169E1  (Links, interactive)
```

#### Semantic Colors
```
Success:             #10B981  (Green - completed tasks)
Warning:             #F59E0B  (Amber - pending actions)
Error:               #EF4444  (Red - urgent/critical)
Info:                #3B82F6  (Blue - informational)
```

#### Neutral Colors
```
Text Primary:        #1F2937  (Dark gray - body text)
Text Secondary:      #6B7280  (Medium gray - labels)
Text Tertiary:       #9CA3AF  (Light gray - metadata)
Border:              #E5E7EB  (Dividers, borders)
Background:          #F9FAFB  (Page background)
Surface:             #FFFFFF  (Cards, modals)
```

#### Dark Mode Palette
```
Background Dark:     #0F172A  (Slate 900)
Surface Dark:        #1E293B  (Slate 800)
Border Dark:         #334155  (Slate 700)
Text Primary Dark:   #F1F5F9  (Slate 100)
Text Secondary Dark: #CBD5E1  (Slate 300)
Periwinkle Dark:     #B8B8FF  (Adjusted for dark bg)
```

### 2.2 Typography

#### Font Families
```
Primary: Inter (sans-serif)
- Clean, modern, excellent readability
- Weights: 400 (Regular), 500 (Medium), 600 (SemiBold), 700 (Bold)

Secondary: SF Pro / Roboto (System defaults)
- Native feel on respective platforms

Monospace: JetBrains Mono
- Code snippets, technical content
```

#### Type Scale
```
Display Large:   32px / 40px line height / Bold
Display Medium:  28px / 36px line height / Bold
Display Small:   24px / 32px line height / SemiBold

Heading 1:       22px / 28px line height / SemiBold
Heading 2:       20px / 28px line height / SemiBold
Heading 3:       18px / 24px line height / SemiBold

Body Large:      16px / 24px line height / Regular
Body Medium:     14px / 20px line height / Regular
Body Small:      12px / 16px line height / Regular

Label Large:     14px / 20px line height / Medium
Label Medium:    12px / 16px line height / Medium
Label Small:     11px / 16px line height / Medium

Caption:         11px / 16px line height / Regular
Overline:        10px / 16px line height / Medium / UPPERCASE
```

### 2.3 Spacing System

```
Space 1:   4px
Space 2:   8px
Space 3:   12px
Space 4:   16px
Space 5:   24px
Space 6:   32px
Space 7:   48px
Space 8:   64px
Space 9:   96px
Space 10:  128px
```

### 2.4 Elevation & Shadows

```
Shadow XS:  0 1px 2px rgba(0, 0, 0, 0.05)
Shadow SM:  0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)
Shadow MD:  0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06)
Shadow LG:  0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)
Shadow XL:  0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04)
Shadow 2XL: 0 25px 50px rgba(0, 0, 0, 0.25)
```

### 2.5 Border Radius

```
Rounded XS:   2px  (Tight elements)
Rounded SM:   4px  (Buttons, small cards)
Rounded MD:   8px  (Cards, modals)
Rounded LG:   12px (Large cards)
Rounded XL:   16px (Feature cards)
Rounded 2XL:  24px (Modal containers)
Rounded Full: 9999px (Pills, avatars)
```

### 2.6 Icons

```
Icon Library: Lucide React (consistent, modern, scalable)
Icon Sizes:  16px, 20px, 24px, 32px, 48px
Icon Style:  2px stroke width, rounded corners
```

---

## 3. Component Design

### 3.1 Navigation

#### Bottom Navigation (Mobile)
```
5 Primary Tabs:
1. Home (Dashboard) - House icon
2. Inbox - Mail icon
3. Calendar - Calendar icon
4. Search - Search icon
5. More - Menu icon

Style:
- Fixed bottom position
- 64px height
- Periwinkle Light background
- Active tab: Periwinkle Dark with icon + label
- Inactive tabs: Gray with icon only
- Smooth tab transition animations
```

#### Top App Bar
```
Components:
- App logo/title (left)
- Search bar (center on desktop)
- Profile avatar (right)
- Notification bell (right)
- Settings gear (right)

Style:
- 56px height (mobile), 64px (desktop)
- White background with bottom shadow
- Sticky position
```

#### Drawer Navigation (Tablet/Desktop)
```
Left Sidebar:
- 280px wide
- Collapsible to 64px (icon-only mode)
- Sections:
  * Quick Actions
  * Mail Folders
  * AI Categories
  * Labels
  * Settings

Style:
- Periwinkle Lighter background
- Active item: Periwinkle Light with left border accent
```

### 3.2 Email List

#### Email List Item
```
Layout (Left to Right):
- Selection checkbox (24px)
- Star icon (24px) - toggleable
- Sender avatar/initial (40px circle)
- Content area:
  * Sender name (SemiBold, 14px)
  * Subject line (Regular, 14px)
  * Preview snippet (Regular, 12px, gray)
  * Metadata row: Priority badge | Date | Category tag
- Attachment indicator (if present)
- Action buttons (hover reveal)

States:
- Unread: Bold sender + subject, Periwinkle Light background
- Read: Normal weight, white background
- Selected: Periwinkle Light background with checkmark
- Priority: Red dot indicator
- AI-flagged: Purple sparkle icon

Height: 72px (compact), 96px (comfortable)
```

#### List Actions Bar
```
Appears when emails selected:
- Select All checkbox
- Archive button
- Delete button
- Mark Read/Unread
- Move to folder
- Apply label
- More actions menu

Style: Periwinkle Dark background, white icons
```

### 3.3 Email Detail View

#### Email Header
```
Components:
- Back button (mobile)
- Subject line (Heading 2)
- Priority badge
- Category tags
- Action buttons row:
  * Reply
  * Reply All
  * Forward
  * Archive
  * Delete
  * More (star, label, move, etc.)

Sender Section:
- Avatar (48px)
- Sender name + email
- Recipients (To, Cc, Bcc - expandable)
- Timestamp
- Show details button
```

#### Email Body
```
- Rich text content rendering
- Inline image display
- Attachment cards at bottom
- Thread view for conversations
- AI Summary card (if available)
- Related emails section (cross-links)
```

#### AI Insights Panel
```
Floating Card Components:
1. Quick Summary
   - Icon: Sparkle
   - 2-3 sentence condensed version
   - "Read full email" link

2. Extracted Information
   - Events detected (date, time, location)
   - Action items list
   - Important dates
   - People mentioned
   - "Add to calendar" buttons

3. Smart Replies
   - 3 suggested replies
   - Tone indicator (professional/casual/urgent)
   - Edit before sending option
   - Regenerate button

4. Related Emails
   - Thread connections
   - Similar category emails
   - Previous correspondence

Style: Periwinkle Light cards with Shadow MD
```

### 3.4 Dashboard

#### Focus Section
```
Hero Card:
- Large card (full width)
- "Focus" heading with icon
- 3-5 most important emails
- Compact list view
- Quick actions (reply, archive)
- "See all priority mail" link

Style: White card, Shadow LG, rounded LG
```

#### Timeline Widget
```
Vertical Timeline:
- Date markers
- Event cards with:
  * Time
  * Title
  * Location (if applicable)
  * Source email link
  * Quick actions (directions, decline, etc.)
- Today marker highlighted

Style: Periwinkle Light cards, vertical line accent
```

#### AI Digest Card
```
Daily/Weekly Summary:
- "Your Day at a Glance" heading
- Bullet points:
  * X new priority emails
  * Y upcoming events
  * Z pending actions
  * Key highlights
- "View detailed report" link

Style: Gradient background (Periwinkle Light to Lilac)
```

#### Quick Stats
```
4 Stat Cards (Row):
1. Unread Count - Mail icon
2. Events Today - Calendar icon
3. Pending Actions - Checklist icon
4. Categories - Folder icon

Style: Small cards, icon + number, colored backgrounds
```

#### Category Sections
```
Horizontal Scrolling Sections:
- Career Opportunities
- HR Announcements
- School Updates
- Financial Alerts
- Each section shows 3-5 recent emails

Style: Card carousel, compact email previews
```

### 3.5 Calendar View

#### Calendar Component
```
Views:
- Day view (timeline with hourly slots)
- Week view (7-column grid)
- Month view (calendar grid)
- Agenda view (list of events)

Event Cards:
- Title
- Time range
- Location
- Source email indicator
- Color-coded by category

Style: Clean grid, Periwinkle accents for today/selected
```

### 3.6 Search Interface

#### Search Bar
```
Components:
- Search icon (left)
- Input field with placeholder "Search emails..."
- Filter button (right)
- Voice search button (right)
- Clear button (when active)

Style: Rounded full, border, focus state with Periwinkle outline
```

#### Search Results
```
Layout:
- Filter chips (category, date, sender)
- Results count
- Sort dropdown
- Email list (same as inbox view)
- AI-powered suggestions

Natural Language Examples:
- "Show me HR emails from last week"
- "Find bills due this month"
- "Job applications with interview dates"
```

#### Filter Panel
```
Expandable Sidebar:
- Date range picker
- Sender selection
- Category checkboxes
- Priority level
- Has attachments toggle
- Read/Unread toggle

Style: Slide-in panel, white background
```

### 3.7 Attachment Hub

#### Attachment Grid
```
Layout Options:
- Grid view (thumbnails)
- List view (detailed)

Attachment Card:
- File icon/thumbnail
- File name
- File size
- Source email preview
- Download button
- Share button

Filters:
- File type (docs, images, PDFs, etc.)
- Date
- Sender
- Category
```

### 3.8 Settings

#### Settings Sections
```
Navigation:
- Account settings
- Appearance (theme, language)
- Notifications
- AI preferences
- Privacy & Security
- About

Style: List items with right chevron, section headers
```

#### Toggle Switches
```
Settings with On/Off:
- Push notifications
- AI analysis
- Smart replies
- Daily digest
- Dark mode

Style: iOS-style toggle, Periwinkle when active
```

### 3.9 Notifications

#### Push Notification
```
Template:
- App icon
- Title (Sender name or "New Priority Email")
- Body (Subject line or summary)
- Timestamp
- Action buttons (Reply, Archive)

Style: System notification style per platform
```

#### In-App Notification
```
Snackbar/Toast:
- Icon (success/info/warning/error)
- Message text
- Action button (optional)
- Dismiss button
- Auto-dismiss after 5 seconds

Position: Bottom center (mobile), Top right (desktop)
Style: Shadow XL, rounded MD
```

#### Notification Center
```
Slide-down Panel:
- Grouped by date
- Notification cards with:
  * Icon
  * Title
  * Message
  * Timestamp
  * Quick action
- Mark all as read button

Style: White background, scrollable list
```

### 3.10 Modals & Dialogs

#### Compose Email Modal
```
Components:
- Header with "New Message" title
- Close button
- Minimize button
- Recipients fields (To, Cc, Bcc)
- Subject line
- Rich text editor toolbar
- Attachment button
- AI assist button (smart replies, tone check)
- Send button (primary)
- Save draft button (secondary)

Style: Full screen (mobile), centered modal (desktop)
Size: 600px wide × 700px tall (desktop)
```

#### Confirmation Dialog
```
Template:
- Icon (warning/info/question)
- Title
- Message text
- Primary action button
- Secondary action button
- Cancel button

Style: Centered, Shadow 2XL, rounded LG
Size: 400px wide × auto height
```

#### Loading States

##### Skeleton Loader
```
Use Cases:
- Email list loading
- Dashboard loading
- Calendar loading

Style:
- Shimmer animation (left to right)
- Periwinkle Lighter background
- Rounded corners matching actual content
- Match layout structure of loaded content
```

##### Spinner
```
Use Cases:
- Button loading states
- Small component updates
- Background sync

Style:
- Circular spinner (Flutter SpinKit)
- Periwinkle Dark color
- Sizes: 16px (inline), 24px (button), 48px (full screen)
```

##### Progress Bar
```
Use Cases:
- Email sync progress
- Attachment upload/download
- AI processing

Style:
- Linear progress indicator
- Periwinkle gradient
- Height: 4px
- Rounded ends
- Percentage text (optional)
```

---

## 4. Screen Designs

### 4.1 Onboarding Flow

#### Welcome Screen
```
Layout:
- App logo (centered, large)
- App name with tagline
- Hero illustration (AI email assistant concept)
- Feature highlights (3-4 cards):
  * AI-powered organization
  * Smart insights
  * Never miss important info
  * Secure & private
- "Get Started" button (primary)
- "Sign in" link (if returning user)

Style: Full screen, gradient background, centered content
```

#### OAuth Login Screen
```
Layout:
- "Connect your Gmail" heading
- Security badge icons
- Permission explanations:
  * Read emails to provide insights
  * Send emails on your behalf
  * Manage labels and folders
  * Privacy commitment statement
- "Sign in with Google" button
- "Learn more" link
- "Cancel" button

Style: Clean, trustworthy design, security emphasis
```

#### Initial Sync Screen
```
Layout:
- "Setting up your inbox" heading
- Progress indicator (percentage)
- Current action text ("Analyzing emails...", "Extracting events...")
- Animated illustration
- Email count processed
- "This may take a few minutes" caption
- "Cancel" button (with confirmation)

Style: Calming animation, progress feedback
```

#### Feature Tutorial
```
Interactive Walkthrough (5-6 screens):
1. Dashboard overview
2. AI insights explanation
3. Smart replies demo
4. Calendar integration
5. Search with natural language
6. Notification settings

Components:
- Screenshot/illustration
- Title
- Description (2-3 sentences)
- "Next" button
- "Skip" link
- Progress dots

Style: Swipeable cards, clear imagery
```

### 4.2 Dashboard (Home)

```
Layout (Scrollable):
1. Header
   - Greeting ("Good morning, [Name]")
   - Date
   - Quick action button

2. AI Digest Card
   - Daily summary
   - Key insights

3. Focus Section
   - Priority emails (3-5)
   - Quick actions

4. Quick Stats Row
   - 4 metric cards

5. Timeline Widget
   - Today's events
   - Upcoming deadlines

6. Category Sections (Horizontal scrollers)
   - Career opportunities
   - HR announcements
   - School updates
   - Financial alerts

7. Recent Activity
   - Last viewed emails
   - Draft messages

Navigation: Bottom tabs visible
Style: Comfortable spacing, scannable layout
```

### 4.3 Inbox Screen

```
Layout:
1. Top App Bar
   - Title "Inbox"
   - Search icon
   - Filter icon
   - Sort menu
   - More options menu

2. Filter Chips Row (Horizontal scroll)
   - All
   - Unread
   - Priority
   - Categories (Career, HR, School, etc.)
   - Custom labels

3. Email List
   - Infinite scroll
   - Pull to refresh
   - Swipe actions:
     * Left: Archive
     * Right: Mark read/unread
   - Long press: Multi-select mode

4. Floating Action Button
   - Compose new email
   - Position: Bottom right

Style: Clean list, clear hierarchy, smooth interactions
```

### 4.4 Email Detail Screen

```
Layout:
1. Header
   - Back button
   - Subject line
   - Action buttons

2. Sender Info Card
   - Avatar
   - Name, email
   - Timestamp
   - Recipients (expandable)

3. AI Insights (Collapsible section)
   - Summary card
   - Extracted info
   - Related emails

4. Email Body
   - Rich text content
   - Inline images
   - Quoted text (collapsible)

5. Attachments Section
   - File cards
   - Download all button

6. Smart Replies (Bottom sheet)
   - 3 suggested replies
   - Quick send buttons

7. Action Bar (Sticky bottom)
   - Reply
   - Reply All
   - Forward

Style: Readable, scannable, action-oriented
```

### 4.5 Calendar Screen

```
Layout:
1. Top Bar
   - View selector (Day/Week/Month/Agenda)
   - Today button
   - Date navigator
   - Add event button

2. Calendar Component
   - Grid or timeline based on view
   - Event cards with:
     * Title
     * Time
     * Location icon
     * Category color
     * Email source indicator

3. Event Detail (Bottom sheet on tap)
   - Full event info
   - Map preview (if location)
   - Source email link
   - Actions: Directions, Add to device calendar, Decline

4. Filters (Drawer)
   - Show/hide categories
   - External calendars toggle

Style: Clean grid, color-coded events, easy navigation
```

### 4.6 Search Screen

```
Layout:
1. Search Bar
   - Auto-focus on screen open
   - Voice search button
   - Recent searches (dropdown)

2. Search Suggestions
   - Natural language examples
   - Category shortcuts
   - Saved searches

3. Results View
   - Filter chips
   - Sort options
   - Results count
   - Email list
   - Load more button

4. Empty State
   - Illustration
   - "Try searching for..." examples
   - Help link

Style: Fast, responsive, helpful suggestions
```

### 4.7 Attachment Hub

```
Layout:
1. Top Bar
   - Title "Attachments"
   - View toggle (Grid/List)
   - Sort menu
   - Search bar

2. Filter Row
   - All files
   - Documents
   - Images
   - PDFs
   - Spreadsheets
   - Other

3. Attachment Grid/List
   - Thumbnail/icon
   - Filename
   - Size
   - Date
   - Source email preview
   - Actions (Download, Share, View)

4. Preview Modal
   - Full-screen preview
   - Download button
   - Share button
   - Open in external app

Style: Visual, organized, quick access
```

### 4.8 Settings Screen

```
Layout:
1. Profile Section
   - Avatar (editable)
   - Name
   - Email address
   - Account switcher

2. Settings List
   - Grouped by category
   - Section headers
   - List items with icons
   - Right chevrons
   - Toggle switches (inline)

3. About Section
   - App version
   - Privacy policy
   - Terms of service
   - Help & support
   - Rate app
   - Logout button

Style: Standard settings layout, clear grouping
```

---

## 5. Interaction Design

### 5.1 Gestures

```
Swipe Actions:
- Swipe left on email: Archive
- Swipe right on email: Mark read/unread
- Pull to refresh: Sync emails
- Long press: Multi-select mode
- Pinch to zoom: Email body (images)

Navigation:
- Swipe from left edge: Open drawer
- Swipe down on top bar: Show notifications
- Swipe between tabs: Calendar views
```

### 5.2 Animations

```
Transitions:
- Screen transitions: 300ms ease-in-out
- Card reveal: 200ms ease-out with scale (0.95 → 1.0)
- List item appearance: Staggered fade-in (50ms delay between items)
- Modal appear: Slide up + fade (300ms)
- Tab change: Cross-fade (200ms)

Micro-interactions:
- Button press: Scale down to 0.95 (100ms)
- Toggle switch: Slide animation (200ms)
- Checkbox: Check mark draw animation (150ms)
- Loading spinner: Continuous rotation
- Shimmer loader: Sweep animation (1.5s loop)
- AI sparkle: Pulse + glow effect (1s)

Feedback:
- Haptic feedback on important actions
- Success animation on email sent
- Error shake animation on validation failure
```

### 5.3 States

```
Interactive Elements:
- Default: Base colors
- Hover: Slight background tint (desktop)
- Focus: Periwinkle outline, 2px
- Active/Pressed: Darker shade
- Disabled: 50% opacity, no pointer events
- Loading: Spinner overlay, pointer disabled

Email States:
- Unread: Bold, background tint
- Read: Normal weight
- Selected: Checkmark, background highlight
- Priority: Red dot, bold
- AI-flagged: Purple sparkle
- Draft: Italic, draft icon
- Sent: Checkmark icon
- Failed: Red warning icon
```

---

## 6. Responsive Design

### 6.1 Breakpoints

```
Mobile:     0 - 640px (single column)
Tablet:     641px - 1024px (adaptive layout)
Desktop:    1025px - 1440px (multi-column)
Large:      1441px+ (max-width container)
```

### 6.2 Layout Adaptations

#### Mobile (Portrait)
```
- Bottom navigation (5 tabs)
- Full-width cards
- Stacked layout
- Drawer menu for secondary nav
- Full-screen modals
- Floating action buttons
```

#### Tablet
```
- Split view (email list + detail)
- Sidebar navigation (collapsible)
- Adaptive grids (2-3 columns)
- Popovers instead of full modals
- Hover states enabled
```

#### Desktop
```
- Three-pane layout (nav + list + detail)
- Permanent sidebar
- Multi-column dashboard
- Keyboard shortcuts enabled
- Context menus on right-click
- Window controls integration
```

---

## 7. Accessibility

### 7.1 Requirements

```
WCAG 2.1 Level AA Compliance:
- Color contrast ratio ≥ 4.5:1 (text)
- Color contrast ratio ≥ 3:1 (UI components)
- All functionality keyboard accessible
- Focus indicators visible
- No flashing content (seizure risk)
- Consistent navigation
```

### 7.2 Screen Reader Support

```
Implementation:
- Semantic HTML/widgets
- ARIA labels on icons
- Descriptive button labels
- Image alt text
- Form field labels
- Error message announcements
- Dynamic content updates announced
```

### 7.3 Keyboard Navigation

```
Shortcuts:
- Tab: Navigate forward
- Shift+Tab: Navigate backward
- Enter/Space: Activate button
- Esc: Close modal/drawer
- Ctrl/Cmd+K: Open search
- C: Compose email
- E: Archive
- R: Reply
- A: Reply all
- F: Forward
- /: Focus search

Visual: Clear focus indicators (Periwinkle outline)
```

### 7.4 Alternative Text

```
Guidelines:
- All images have alt text
- Decorative images: empty alt
- Icons paired with labels or tooltips
- Complex graphics: detailed descriptions
- Charts: Data table alternative
```

---

## 8. Dark Mode

### 8.1 Color Adaptations

```
Backgrounds:
- Page: #0F172A (Slate 900)
- Surface: #1E293B (Slate 800)
- Elevated: #334155 (Slate 700)

Text:
- Primary: #F1F5F9 (Slate 100)
- Secondary: #CBD5E1 (Slate 300)
- Tertiary: #94A3B8 (Slate 400)

Periwinkle Adjustments:
- Primary: #B8B8FF (Lighter for contrast)
- Accent: #9999FF
- Background: #2D2D4D (Darker periwinkle)
```

### 8.2 Image Handling

```
- Reduce image brightness by 10-15%
- Apply slight gray overlay to photos
- Invert icons to light versions
- Preserve brand colors in logos
```

### 8.3 Toggle

```
Location: Settings > Appearance
Options:
- Light
- Dark
- System default (auto-switch)

Implementation: Smooth transition (300ms) when switching
```

---

## 9. Brand Identity

### 9.1 Logo Design

```
Concept:
- Envelope icon with AI sparkle
- Periwinkle color scheme
- Modern, geometric style
- Scalable (16px to 512px)

Variations:
- Full logo (icon + wordmark)
- Icon only
- Monochrome (white/black)
```

### 9.2 App Icon

```
Design:
- Rounded square (iOS) / Adaptive (Android)
- Periwinkle gradient background
- White envelope with sparkle
- Clean, recognizable at small sizes
```

### 9.3 Splash Screen

```
Layout:
- Centered logo
- App name below
- Loading spinner
- Background: Periwinkle gradient

Duration: 1-2 seconds (only on cold start)
```

---

## 10. Illustrations & Graphics

### 10.1 Illustration Style

```
Characteristics:
- Flat design with subtle shadows
- Periwinkle and purple color palette
- Friendly, approachable characters
- Technology and productivity themes
- Consistent line weights
```

### 10.2 Empty States

```
Use Cases:
- No emails in category
- No search results
- No attachments
- No calendar events

Components:
- Illustration (themed to context)
- Heading
- Description text
- Call-to-action button
```

### 10.3 Error States

```
Types:
- Network error
- Sync failed
- Permission denied
- Something went wrong

Components:
- Error illustration
- Error message
- Helpful suggestion
- Retry button
```

---

## 11. Platform-Specific Considerations

### 11.1 iOS

```
Design Elements:
- SF Pro font
- iOS-style navigation bar
- Bottom tab bar (iOS style)
- Swipe gestures (back navigation)
- iOS share sheet
- Haptic feedback
- Face ID / Touch ID integration
```

### 11.2 Android

```
Design Elements:
- Roboto font
- Material Design components
- Floating action buttons
- Material ripple effects
- Android share sheet
- Fingerprint authentication
- System navigation gestures
```

### 11.3 Web

```
Design Elements:
- Responsive layout
- Keyboard shortcuts
- Browser notifications
- Right-click context menus
- Window resize handling
- Progressive Web App features
```

### 11.4 Desktop (Windows/macOS/Linux)

```
Design Elements:
- Window controls
- Menu bar (macOS)
- System tray icon
- Native notifications
- Drag & drop support
- Multi-window support
```

---

## 12. Design Deliverables

### 12.1 Design Files

```
Required:
- Figma design system file
- Component library
- Screen designs (all breakpoints)
- Prototype flows
- Icon set
- Illustration library
- Export assets (all resolutions)
```

### 12.2 Documentation

```
Included:
- Style guide
- Component specifications
- Interaction patterns
- Animation guidelines
- Accessibility checklist
- Platform adaptations
```

---

## 13. Design Principles Summary

1. **Clarity**: Every element has a clear purpose
2. **Consistency**: Unified visual language throughout
3. **Feedback**: Immediate response to user actions
4. **Efficiency**: Minimize steps to complete tasks
5. **Forgiveness**: Easy to undo mistakes
6. **Accessibility**: Usable by everyone
7. **Performance**: Fast, smooth, responsive
8. **Trust**: Transparent AI, secure design
9. **Delight**: Pleasant, enjoyable experience
10. **Focus**: Surface what matters, hide the rest