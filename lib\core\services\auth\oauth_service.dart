import 'package:google_sign_in/google_sign_in.dart';
import 'package:get/get.dart';
import 'package:dio/dio.dart';
import '../storage/secure_storage_service.dart';
import '../../constants/api_constants.dart';
import '../../models/user_model.dart';
import '../../config/env_config.dart';

class OAuthService extends GetxService {
  late GoogleSignIn _googleSignIn;
  final Dio _dio = Dio();

  @override
  void onInit() {
    super.onInit();
    _initializeGoogleSignIn();
  }

  void _initializeGoogleSignIn() {
    _googleSignIn = GoogleSignIn(
      scopes: ApiConstants.gmailScopes,
      serverClientId: EnvConfig.webClientId.isNotEmpty && EnvConfig.webClientId != 'your_web_client_id_here' 
          ? EnvConfig.webClientId 
          : null,
    );
  }

  Future<UserModel?> signInWithGoogle() async {
    try {
      // Check if already signed in
      GoogleSignInAccount? account = _googleSignIn.currentUser;
      
      // If not signed in, initiate sign in
      if (account == null) {
        account = await _googleSignIn.signIn();
      }
      
      if (account == null) {
        // User cancelled sign in
        return null;
      }

      final GoogleSignInAuthentication auth = await account.authentication;
      
      // Validate tokens
      if (auth.accessToken == null) {
        throw Exception('Failed to get access token');
      }
      
      // Store tokens securely
      await SecureStorageService.write('access_token', auth.accessToken!);
      if (auth.idToken != null) {
        await SecureStorageService.write('id_token', auth.idToken!);
      }
      await SecureStorageService.write('refresh_token', auth.serverAuthCode ?? '');
      
      // Store token expiry time (Google tokens typically expire in 1 hour)
      final expiryTime = DateTime.now().add(Duration(minutes: 55)).millisecondsSinceEpoch;
      await SecureStorageService.write('token_expiry', expiryTime.toString());

      final user = UserModel(
        id: account.id,
        email: account.email,
        name: account.displayName ?? account.email.split('@')[0],
        profilePicture: account.photoUrl,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
      );
      
      return user;
    } catch (e) {
      print('Google Sign In Error: $e');
      Get.snackbar(
        'Authentication Error', 
        'Failed to sign in with Google: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
      return null;
    }
  }

  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      await _googleSignIn.disconnect();
      await SecureStorageService.deleteAll();
    } catch (e) {
      print('Sign out error: $e');
      // Still clear local storage even if Google sign out fails
      await SecureStorageService.deleteAll();
    }
  }

  Future<String?> getValidAccessToken() async {
    try {
      final token = await SecureStorageService.read('access_token');
      if (token == null) return null;
      
      // Check if token is expired
      final expiryStr = await SecureStorageService.read('token_expiry');
      if (expiryStr != null) {
        final expiry = DateTime.fromMillisecondsSinceEpoch(int.parse(expiryStr));
        if (DateTime.now().isAfter(expiry)) {
          // Token expired, try to refresh
          return await _refreshAccessToken();
        }
      }
      
      return token;
    } catch (e) {
      print('Error getting access token: $e');
      return null;
    }
  }

  Future<String?> _refreshAccessToken() async {
    try {
      final account = _googleSignIn.currentUser;
      if (account == null) return null;
      
      // Force refresh the authentication
      final auth = await account.authentication;
      
      if (auth.accessToken != null) {
        await SecureStorageService.write('access_token', auth.accessToken!);
        final expiryTime = DateTime.now().add(Duration(minutes: 55)).millisecondsSinceEpoch;
        await SecureStorageService.write('token_expiry', expiryTime.toString());
        return auth.accessToken;
      }
      
      return null;
    } catch (e) {
      print('Error refreshing token: $e');
      return null;
    }
  }

  Future<bool> isSignedIn() async {
    try {
      final isSignedIn = await _googleSignIn.isSignedIn();
      final hasToken = await SecureStorageService.read('access_token') != null;
      return isSignedIn && hasToken;
    } catch (e) {
      return false;
    }
  }

  Future<GoogleSignInAccount?> getCurrentUser() async {
    return _googleSignIn.currentUser;
  }

  // Silent sign in for app startup
  Future<UserModel?> silentSignIn() async {
    try {
      final account = await _googleSignIn.signInSilently();
      if (account == null) return null;
      
      final auth = await account.authentication;
      if (auth.accessToken == null) return null;
      
      return UserModel(
        id: account.id,
        email: account.email,
        name: account.displayName ?? account.email.split('@')[0],
        profilePicture: account.photoUrl,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
      );
    } catch (e) {
      print('Silent sign in failed: $e');
      return null;
    }
  }
}