import 'package:google_sign_in/google_sign_in.dart';
import 'package:get/get.dart';
import 'package:dio/dio.dart';
import '../storage/secure_storage_service.dart';
import '../logger/logger_service.dart';
import '../../constants/api_constants.dart';
import '../../models/user_model.dart';
import '../../config/env_config.dart';

/// Enhanced OAuth Service with proper error handling and token management
class OAuthService extends GetxService {
  static OAuthService get instance => Get.find<OAuthService>();

  late GoogleSignIn _googleSignIn;
  final Dio _dio = Dio();

  @override
  void onInit() {
    super.onInit();
    _initializeGoogleSignIn();
    LoggerService.info('OAuthService initialized', 'OAUTH');
  }

  void _initializeGoogleSignIn() {
    _googleSignIn = GoogleSignIn(
      scopes: ApiConstants.gmailScopes,
      serverClientId: EnvConfig.webClientId.isNotEmpty && EnvConfig.webClientId != 'your_web_client_id_here'
          ? EnvConfig.webClientId
          : null,
    );
    LoggerService.debug('Google Sign-In initialized with scopes: ${ApiConstants.gmailScopes}', 'OAUTH');
  }

  Future<UserModel?> signInWithGoogle() async {
    try {
      // Check if already signed in
      GoogleSignInAccount? account = _googleSignIn.currentUser;
      
      // If not signed in, initiate sign in
      if (account == null) {
        account = await _googleSignIn.signIn();
      }
      
      if (account == null) {
        // User cancelled sign in
        return null;
      }

      final GoogleSignInAuthentication auth = await account.authentication;
      
      // Validate tokens
      if (auth.accessToken == null) {
        throw Exception('Failed to get access token');
      }

      // Store tokens securely
      final secureStorage = Get.find<SecureStorageService>();
      await secureStorage.write('access_token', auth.accessToken!);
      if (auth.idToken != null) {
        await secureStorage.write('id_token', auth.idToken!);
      }
      // Note: Google Sign-In doesn't provide refresh tokens directly
      // We'll handle token refresh through the Google Sign-In SDK

      // Store token expiry time (Google tokens typically expire in 1 hour)
      final expiryTime = DateTime.now().add(Duration(minutes: 55)).millisecondsSinceEpoch;
      await secureStorage.write('token_expiry', expiryTime.toString());

      final user = UserModel(
        id: account.id,
        email: account.email,
        name: account.displayName ?? account.email.split('@')[0],
        profilePicture: account.photoUrl,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
      );
      
      return user;
    } catch (e) {
      LoggerService.error('Google Sign-In failed', e, null, 'OAUTH');
      Get.snackbar(
        'Authentication Error',
        'Failed to sign in with Google: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
      return null;
    }
  }

  /// Sign out from Google and clear all stored tokens
  Future<void> signOut() async {
    try {
      LoggerService.info('Starting sign out process', 'OAUTH');
      await _googleSignIn.signOut();
      await _googleSignIn.disconnect();

      final secureStorage = Get.find<SecureStorageService>();
      await secureStorage.deleteAll();

      LoggerService.info('Sign out completed successfully', 'OAUTH');
    } catch (e) {
      LoggerService.error('Sign out error', e, null, 'OAUTH');
      // Still clear local storage even if Google sign out fails
      final secureStorage = Get.find<SecureStorageService>();
      await secureStorage.deleteAll();
    }
  }

  /// Get a valid access token
  Future<String?> getValidAccessToken() async {
    try {
      final secureStorage = Get.find<SecureStorageService>();
      final token = await secureStorage.read('access_token');
      if (token == null) {
        LoggerService.warning('No access token found', 'OAUTH');
        return null;
      }

      // Check if token is expired
      final expiryStr = await secureStorage.read('token_expiry');
      if (expiryStr != null) {
        final expiry = DateTime.fromMillisecondsSinceEpoch(int.parse(expiryStr));
        if (DateTime.now().isAfter(expiry)) {
          LoggerService.warning('Access token expired', 'OAUTH');
          return null; // Token expired, user needs to re-authenticate
        }
      }

      LoggerService.debug('Valid access token retrieved', 'OAUTH');
      return token;
    } catch (e) {
      LoggerService.error('Error getting access token', e, null, 'OAUTH');
      return null;
    }
  }

  /// Check if user is currently signed in
  Future<bool> isSignedIn() async {
    try {
      final secureStorage = Get.find<SecureStorageService>();
      final hasToken = await secureStorage.read('access_token') != null;
      LoggerService.debug('Sign-in status check: hasToken=$hasToken', 'OAUTH');
      return hasToken;
    } catch (e) {
      LoggerService.error('Error checking sign-in status', e, null, 'OAUTH');
      return false;
    }
  }
}