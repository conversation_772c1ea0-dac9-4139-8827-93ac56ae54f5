class EmailModel {
  final String id;
  final String threadId;
  final String subject;
  final String snippet;
  final String from;
  final List<String> to;
  final List<String>? cc;
  final List<String>? bcc;
  final DateTime date;
  final bool isRead;
  final bool isStarred;
  final List<String> labels;
  final String? body;
  final List<AttachmentModel>? attachments;

  EmailModel({
    required this.id,
    required this.threadId,
    required this.subject,
    required this.snippet,
    required this.from,
    required this.to,
    this.cc,
    this.bcc,
    required this.date,
    required this.isRead,
    required this.isStarred,
    required this.labels,
    this.body,
    this.attachments,
  });

  factory EmailModel.fromJson(Map<String, dynamic> json) {
    return EmailModel(
      id: json['id'],
      threadId: json['threadId'],
      subject: json['subject'],
      snippet: json['snippet'],
      from: json['from'],
      to: List<String>.from(json['to']),
      cc: json['cc'] != null ? List<String>.from(json['cc']) : null,
      bcc: json['bcc'] != null ? List<String>.from(json['bcc']) : null,
      date: DateTime.parse(json['date']),
      isRead: json['isRead'],
      isStarred: json['isStarred'],
      labels: List<String>.from(json['labels']),
      body: json['body'],
      attachments: json['attachments'] != null
          ? (json['attachments'] as List)
              .map((a) => AttachmentModel.fromJson(a))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'threadId': threadId,
      'subject': subject,
      'snippet': snippet,
      'from': from,
      'to': to,
      'cc': cc,
      'bcc': bcc,
      'date': date.toIso8601String(),
      'isRead': isRead,
      'isStarred': isStarred,
      'labels': labels,
      'body': body,
      'attachments': attachments?.map((a) => a.toJson()).toList(),
    };
  }
}

class AttachmentModel {
  final String id;
  final String filename;
  final String mimeType;
  final int size;

  AttachmentModel({
    required this.id,
    required this.filename,
    required this.mimeType,
    required this.size,
  });

  factory AttachmentModel.fromJson(Map<String, dynamic> json) {
    return AttachmentModel(
      id: json['id'],
      filename: json['filename'],
      mimeType: json['mimeType'],
      size: json['size'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'filename': filename,
      'mimeType': mimeType,
      'size': size,
    };
  }
}