import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../logger/logger_service.dart';

/// Enhanced Local Storage Service with better error handling and type safety
class LocalStorageService extends GetxService {
  static LocalStorageService get instance => Get.find<LocalStorageService>();

  late GetStorage _box;
  bool _isInitialized = false;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeStorage();
  }

  Future<void> _initializeStorage() async {
    try {
      await GetStorage.init('app_storage');
      _box = GetStorage('app_storage');
      _isInitialized = true;
      LoggerService.info('LocalStorageService initialized successfully', 'STORAGE');
    } catch (e) {
      LoggerService.error('Failed to initialize LocalStorageService', e, null, 'STORAGE');
      rethrow;
    }
  }

  /// Generic read method with type safety
  T? read<T>(String key) {
    if (!_isInitialized) {
      LoggerService.warning('Storage not initialized, cannot read key: $key', 'STORAGE');
      return null;
    }

    try {
      final value = _box.read<T>(key);
      LoggerService.debug('Read from storage - Key: $key, HasValue: ${value != null}', 'STORAGE');
      return value;
    } catch (e) {
      LoggerService.error('Error reading from storage - Key: $key', e, null, 'STORAGE');
      return null;
    }
  }

  /// Generic write method with validation
  Future<bool> write(String key, Object? value) async {
    if (!_isInitialized) {
      LoggerService.warning('Storage not initialized, cannot write key: $key', 'STORAGE');
      return false;
    }

    try {
      await _box.write(key, value);
      LoggerService.debug('Written to storage - Key: $key', 'STORAGE');
      return true;
    } catch (e) {
      LoggerService.error('Error writing to storage - Key: $key', e, null, 'STORAGE');
      return false;
    }
  }

  /// Remove a specific key
  Future<bool> remove(String key) async {
    if (!_isInitialized) {
      LoggerService.warning('Storage not initialized, cannot remove key: $key', 'STORAGE');
      return false;
    }

    try {
      await _box.remove(key);
      LoggerService.debug('Removed from storage - Key: $key', 'STORAGE');
      return true;
    } catch (e) {
      LoggerService.error('Error removing from storage - Key: $key', e, null, 'STORAGE');
      return false;
    }
  }

  /// Clear all storage data
  Future<bool> clear() async {
    if (!_isInitialized) {
      LoggerService.warning('Storage not initialized, cannot clear', 'STORAGE');
      return false;
    }

    try {
      await _box.erase();
      LoggerService.info('Storage cleared successfully', 'STORAGE');
      return true;
    } catch (e) {
      LoggerService.error('Error clearing storage', e, null, 'STORAGE');
      return false;
    }
  }

  /// Check if a key exists
  bool hasData(String key) {
    if (!_isInitialized) {
      LoggerService.warning('Storage not initialized, cannot check key: $key', 'STORAGE');
      return false;
    }

    try {
      final exists = _box.hasData(key);
      LoggerService.debug('Key exists check - Key: $key, Exists: $exists', 'STORAGE');
      return exists;
    } catch (e) {
      LoggerService.error('Error checking storage - Key: $key', e, null, 'STORAGE');
      return false;
    }
  }

  /// Get all keys in storage
  List<String> getAllKeys() {
    if (!_isInitialized) {
      LoggerService.warning('Storage not initialized, cannot get keys', 'STORAGE');
      return [];
    }

    try {
      final keys = _box.getKeys().cast<String>().toList();
      LoggerService.debug('Retrieved ${keys.length} keys from storage', 'STORAGE');
      return keys;
    } catch (e) {
      LoggerService.error('Error getting all keys from storage', e, null, 'STORAGE');
      return [];
    }
  }

  /// Get storage size (number of items)
  int getSize() {
    if (!_isInitialized) {
      LoggerService.warning('Storage not initialized, cannot get size', 'STORAGE');
      return 0;
    }

    try {
      final size = _box.getKeys().length;
      LoggerService.debug('Storage size: $size items', 'STORAGE');
      return size;
    } catch (e) {
      LoggerService.error('Error getting storage size', e, null, 'STORAGE');
      return 0;
    }
  }

  /// Write multiple key-value pairs
  Future<bool> writeAll(Map<String, Object?> data) async {
    if (!_isInitialized) {
      LoggerService.warning('Storage not initialized, cannot write multiple keys', 'STORAGE');
      return false;
    }

    try {
      for (final entry in data.entries) {
        await _box.write(entry.key, entry.value);
      }
      LoggerService.debug('Written ${data.length} items to storage', 'STORAGE');
      return true;
    } catch (e) {
      LoggerService.error('Error writing multiple items to storage', e, null, 'STORAGE');
      return false;
    }
  }

  /// Read multiple keys at once
  Map<String, T?> readAll<T>(List<String> keys) {
    if (!_isInitialized) {
      LoggerService.warning('Storage not initialized, cannot read multiple keys', 'STORAGE');
      return {};
    }

    try {
      final result = <String, T?>{};
      for (final key in keys) {
        result[key] = _box.read<T>(key);
      }
      LoggerService.debug('Read ${keys.length} items from storage', 'STORAGE');
      return result;
    } catch (e) {
      LoggerService.error('Error reading multiple items from storage', e, null, 'STORAGE');
      return {};
    }
  }

  /// Check if storage is initialized
  bool get isInitialized => _isInitialized;
}