import 'package:get_storage/get_storage.dart';

class LocalStorageService {
  static final LocalStorageService _instance = LocalStorageService._internal();
  factory LocalStorageService() => _instance;
  LocalStorageService._internal() {
    _box = GetStorage();
  }

  late GetStorage _box;

  Future<void> init() async {
    await GetStorage.init();
    _box = GetStorage();
  }

  // Generic methods
  T? read<T>(String key) {
    try {
      return _box.read<T>(key);
    } catch (e) {
      print('Error reading from storage: $e');
      return null;
    }
  }
  
  Future<void> write(String key, dynamic value) async {
    try {
      await _box.write(key, value);
    } catch (e) {
      print('Error writing to storage: $e');
    }
  }
  
  Future<void> remove(String key) async {
    try {
      await _box.remove(key);
    } catch (e) {
      print('Error removing from storage: $e');
    }
  }
  
  Future<void> clear() async {
    try {
      await _box.erase();
    } catch (e) {
      print('Error clearing storage: $e');
    }
  }
  
  bool hasData(String key) {
    try {
      return _box.hasData(key);
    } catch (e) {
      print('Error checking storage: $e');
      return false;
    }
  }
}