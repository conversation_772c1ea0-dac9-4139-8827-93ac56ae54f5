import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/oauth_controller.dart';

class LoginScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final OauthController controller = Get.put(OauthController());
    
    return Scaffold(
      body: Safe<PERSON>rea(
        child: Padding(
          padding: EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // App Logo/Title
              Icon(
                Icons.email,
                size: 80,
                color: Colors.blue,
              ),
              SizedBox(height: 24),
              Text(
                'AI Email Client',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'Transform your Gmail into an intelligent assistant',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
              ),
              Sized<PERSON>ox(height: 48),
              
              // Error message
              Obx(() => controller.errorMessage.value.isNotEmpty
                  ? Container(
                      padding: EdgeInsets.all(12),
                      margin: EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: Colors.red[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red[200]!),
                      ),
                      child: Text(
                        controller.errorMessage.value,
                        style: TextStyle(color: Colors.red[700]),
                        textAlign: TextAlign.center,
                      ),
                    )
                  : SizedBox.shrink()),
              
              // Google Sign In Button
              Obx(() => ElevatedButton.icon(
                onPressed: controller.isLoading.value 
                    ? null 
                    : controller.signInWithGoogle,
                icon: controller.isLoading.value
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Icon(Icons.login),
                label: Text(
                  controller.isLoading.value 
                      ? 'Signing in...' 
                      : 'Sign in with Google',
                ),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              )),
              
              SizedBox(height: 24),
              
              // Privacy notice
              Text(
                'By signing in, you agree to our Terms of Service and Privacy Policy. We only access your Gmail data to provide email management features.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}