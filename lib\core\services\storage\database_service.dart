import 'dart:convert';
import 'package:get_storage/get_storage.dart';
import 'package:get/get.dart';
import '../logger/logger_service.dart';

/// Database service that uses GetStorage as the underlying storage mechanism
/// Provides a simple interface for storing and retrieving models
class DatabaseService extends GetxService {
  static DatabaseService get instance => Get.find<DatabaseService>();

  late GetStorage _storage;

  // Collection names
  static const String _usersCollection = 'users';
  static const String _emailsCollection = 'emails';
  static const String _labelsCollection = 'labels';
  static const String _attachmentsCollection = 'attachments';
  static const String _insightsCollection = 'ai_insights';
  static const String _eventsCollection = 'calendar_events';
  static const String _notificationsCollection = 'notifications';
  static const String _settingsCollection = 'settings';

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeStorage();
    LoggerService.info('DatabaseService initialized');
  }

  Future<void> _initializeStorage() async {
    try {
      await GetStorage.init('ai_email_database');
      _storage = GetStorage('ai_email_database');
      LoggerService.info('Database storage initialized successfully');
    } catch (e) {
      LoggerService.error('Failed to initialize database storage', e);
      rethrow;
    }
  }

  /// Generic method to save a model to a collection
  Future<bool> save<T>(String collection, String id, T model) async {
    try {
      final data = _modelToMap(model);
      data['id'] = id;
      data['updated_at'] = DateTime.now().millisecondsSinceEpoch;
      data['created_at'] ??= DateTime.now().millisecondsSinceEpoch;

      final collectionData = _getCollection(collection);
      collectionData[id] = data;

      await _storage.write(collection, collectionData);
      LoggerService.debug('Saved model to $collection with id: $id');
      return true;
    } catch (e) {
      LoggerService.error('Failed to save model to $collection', e);
      return false;
    }
  }

  /// Generic method to get a model from a collection
  T? get<T>(String collection, String id, T Function(Map<String, dynamic>) fromMap) {
    try {
      final collectionData = _getCollection(collection);
      final data = collectionData[id];

      if (data != null) {
        return fromMap(Map<String, dynamic>.from(data));
      }
      return null;
    } catch (e) {
      LoggerService.error('Failed to get model from $collection with id: $id', e);
      return null;
    }
  }

  /// Generic method to get all models from a collection
  List<T> getAll<T>(String collection, T Function(Map<String, dynamic>) fromMap) {
    try {
      final collectionData = _getCollection(collection);
      final List<T> models = [];

      for (final data in collectionData.values) {
        try {
          final model = fromMap(Map<String, dynamic>.from(data));
          models.add(model);
        } catch (e) {
          LoggerService.warning('Failed to parse model in $collection: $e');
        }
      }

      return models;
    } catch (e) {
      LoggerService.error('Failed to get all models from $collection', e);
      return [];
    }
  }

  /// Generic method to delete a model from a collection
  Future<bool> delete(String collection, String id) async {
    try {
      final collectionData = _getCollection(collection);
      collectionData.remove(id);

      await _storage.write(collection, collectionData);
      LoggerService.debug('Deleted model from $collection with id: $id');
      return true;
    } catch (e) {
      LoggerService.error('Failed to delete model from $collection', e);
      return false;
    }
  }

  /// Generic method to update a model in a collection
  Future<bool> update<T>(String collection, String id, Map<String, dynamic> updates) async {
    try {
      final collectionData = _getCollection(collection);
      final existingData = collectionData[id];

      if (existingData != null) {
        final updatedData = Map<String, dynamic>.from(existingData);
        updatedData.addAll(updates);
        updatedData['updated_at'] = DateTime.now().millisecondsSinceEpoch;

        collectionData[id] = updatedData;
        await _storage.write(collection, collectionData);
        LoggerService.debug('Updated model in $collection with id: $id');
        return true;
      }

      LoggerService.warning('Model not found in $collection with id: $id');
      return false;
    } catch (e) {
      LoggerService.error('Failed to update model in $collection', e);
      return false;
    }
  }

  /// Query models with conditions
  List<T> query<T>(
    String collection,
    T Function(Map<String, dynamic>) fromMap,
    bool Function(Map<String, dynamic>) where,
  ) {
    try {
      final collectionData = _getCollection(collection);
      final List<T> models = [];

      for (final data in collectionData.values) {
        try {
          final dataMap = Map<String, dynamic>.from(data);
          if (where(dataMap)) {
            final model = fromMap(dataMap);
            models.add(model);
          }
        } catch (e) {
          LoggerService.warning('Failed to parse model in query for $collection: $e');
        }
      }

      return models;
    } catch (e) {
      LoggerService.error('Failed to query models from $collection', e);
      return [];
    }
  }

  /// Check if a model exists in a collection
  bool exists(String collection, String id) {
    try {
      final collectionData = _getCollection(collection);
      return collectionData.containsKey(id);
    } catch (e) {
      LoggerService.error('Failed to check existence in $collection', e);
      return false;
    }
  }

  /// Get count of models in a collection
  int count(String collection) {
    try {
      final collectionData = _getCollection(collection);
      return collectionData.length;
    } catch (e) {
      LoggerService.error('Failed to count models in $collection', e);
      return 0;
    }
  }

  /// Clear all data in a collection
  Future<bool> clearCollection(String collection) async {
    try {
      await _storage.write(collection, <String, dynamic>{});
      LoggerService.info('Cleared collection: $collection');
      return true;
    } catch (e) {
      LoggerService.error('Failed to clear collection: $collection', e);
      return false;
    }
  }

  /// Clear all data in the database
  Future<bool> clearAll() async {
    try {
      await _storage.erase();
      LoggerService.info('Cleared all database data');
      return true;
    } catch (e) {
      LoggerService.error('Failed to clear all database data', e);
      return false;
    }
  }

  // Helper methods

  /// Get collection data from storage
  Map<String, dynamic> _getCollection(String collection) {
    final data = _storage.read(collection);
    if (data is Map) {
      return Map<String, dynamic>.from(data);
    }
    return <String, dynamic>{};
  }

  /// Convert model to map for storage
  Map<String, dynamic> _modelToMap<T>(T model) {
    if (model is Map<String, dynamic>) {
      return Map<String, dynamic>.from(model);
    } else if (model is Map) {
      return Map<String, dynamic>.from(model.cast<String, dynamic>());
    } else {
      // Try to convert using toJson if available
      try {
        final dynamic json = (model as dynamic).toJson();
        if (json is Map<String, dynamic>) {
          return json;
        } else if (json is Map) {
          return Map<String, dynamic>.from(json.cast<String, dynamic>());
        }
      } catch (e) {
        LoggerService.warning('Model does not have toJson method, using toString: $e');
      }

      // Fallback: create a simple map representation
      return {'data': model.toString(), 'type': model.runtimeType.toString()};
    }
  }

  // Convenience methods for specific collections

  /// Save user data
  Future<bool> saveUser(String id, Map<String, dynamic> userData) async {
    return await save(_usersCollection, id, userData);
  }

  /// Get user data
  Map<String, dynamic>? getUser(String id) {
    return get(_usersCollection, id, (data) => data);
  }

  /// Save email data
  Future<bool> saveEmail(String id, Map<String, dynamic> emailData) async {
    return await save(_emailsCollection, id, emailData);
  }

  /// Get email data
  Map<String, dynamic>? getEmail(String id) {
    return get(_emailsCollection, id, (data) => data);
  }

  /// Get all emails
  List<Map<String, dynamic>> getAllEmails() {
    return getAll(_emailsCollection, (data) => data);
  }

  /// Save label data
  Future<bool> saveLabel(String id, Map<String, dynamic> labelData) async {
    return await save(_labelsCollection, id, labelData);
  }

  /// Get all labels
  List<Map<String, dynamic>> getAllLabels() {
    return getAll(_labelsCollection, (data) => data);
  }

  /// Save AI insight
  Future<bool> saveInsight(String id, Map<String, dynamic> insightData) async {
    return await save(_insightsCollection, id, insightData);
  }

  /// Get insights for email
  List<Map<String, dynamic>> getInsightsForEmail(String emailId) {
    return query(_insightsCollection, (data) => data, (data) => data['email_id'] == emailId);
  }

  /// Save calendar event
  Future<bool> saveEvent(String id, Map<String, dynamic> eventData) async {
    return await save(_eventsCollection, id, eventData);
  }

  /// Get events in date range
  List<Map<String, dynamic>> getEventsInRange(int startTime, int endTime) {
    return query(_eventsCollection, (data) => data, (data) {
      final eventStart = data['start_time'] as int?;
      return eventStart != null && eventStart >= startTime && eventStart <= endTime;
    });
  }

  /// Save app settings
  Future<bool> saveSettings(String key, dynamic value) async {
    return await save(_settingsCollection, key, {'value': value});
  }

  /// Get app setting
  T? getSetting<T>(String key) {
    final data = get(_settingsCollection, key, (data) => data);
    return data?['value'] as T?;
  }
}