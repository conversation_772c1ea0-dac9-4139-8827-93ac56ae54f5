import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseService {
  static Database? _database;
  static const String _databaseName = 'ai_email_client.db';
  static const int _databaseVersion = 1;

  static Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  static Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), _databaseName);
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  static Future<void> _onCreate(Database db, int version) async {
    await _createTables(db);
  }

  static Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades
  }

  static Future<void> _createTables(Database db) async {
    // Users table
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        email TEXT NOT NULL UNIQUE,
        name TEXT,
        avatar_url TEXT,
        created_at INTEGER,
        updated_at INTEGER,
        settings TEXT
      )
    ''');

    // Emails table
    await db.execute('''
      CREATE TABLE emails (
        id TEXT PRIMARY KEY,
        gmail_id TEXT UNIQUE,
        thread_id TEXT,
        user_id TEXT,
        from_email TEXT,
        from_name TEXT,
        to_emails TEXT,
        cc_emails TEXT,
        bcc_emails TEXT,
        subject TEXT,
        snippet TEXT,
        body_plain TEXT,
        body_html TEXT,
        date INTEGER,
        is_read INTEGER DEFAULT 0,
        is_starred INTEGER DEFAULT 0,
        is_priority INTEGER DEFAULT 0,
        is_archived INTEGER DEFAULT 0,
        has_attachments INTEGER DEFAULT 0,
        labels TEXT,
        category TEXT,
        ai_summary TEXT,
        ai_priority_score REAL,
        created_at INTEGER,
        updated_at INTEGER,
        FOREIGN KEY (user_id) REFERENCES users(id)
      )
    ''');

    // Create indexes
    await db.execute('CREATE INDEX idx_emails_user_id ON emails(user_id)');
    await db.execute('CREATE INDEX idx_emails_date ON emails(date DESC)');
    await db.execute('CREATE INDEX idx_emails_category ON emails(category)');
  }

  static Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }
}