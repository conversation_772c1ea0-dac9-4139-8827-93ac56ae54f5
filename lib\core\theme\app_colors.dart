import 'package:flutter/material.dart';

/// App color constants based on Periwinkle theme
class AppColors {
  AppColors._();

  // Primary Colors (Periwinkle Theme)
  static const Color periwinkleBase = Color(0xFFCCCCFF);
  static const Color periwinkleDark = Color(0xFF9999CC);
  static const Color periwinkleDarker = Color(0xFF666699);
  static const Color periwinkleLight = Color(0xFFE6E6FF);
  static const Color periwinkleLighter = Color(0xFFF5F5FF);

  // Secondary Colors
  static const Color lavender = Color(0xFFE6E6FA);
  static const Color lilac = Color(0xFFC8A2C8);
  static const Color slateBlue = Color(0xFF6A5ACD);
  static const Color royalBlue = Color(0xFF4169E1);

  // Semantic Colors
  static const Color success = Color(0xFF10B981);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);
  static const Color info = Color(0xFF3B82F6);

  // Neutral Colors (Light Mode)
  static const Color textPrimary = Color(0xFF1F2937);
  static const Color textSecondary = Color(0xFF6B7280);
  static const Color textTertiary = Color(0xFF9CA3AF);
  static const Color border = Color(0xFFE5E7EB);
  static const Color background = Color(0xFFF9FAFB);
  static const Color surface = Color(0xFFFFFFFF);

  // Dark Mode Colors
  static const Color backgroundDark = Color(0xFF0F172A);
  static const Color surfaceDark = Color(0xFF1E293B);
  static const Color borderDark = Color(0xFF334155);
  static const Color textPrimaryDark = Color(0xFFF1F5F9);
  static const Color textSecondaryDark = Color(0xFFCBD5E1);
  static const Color periwinkleDarkMode = Color(0xFFB8B8FF);

  // Material Color Swatches
  static const MaterialColor periwinkleSwatch = MaterialColor(
    0xFFCCCCFF,
    <int, Color>{
      50: Color(0xFFF5F5FF),
      100: Color(0xFFE6E6FF),
      200: Color(0xFFCCCCFF),
      300: Color(0xFFB3B3FF),
      400: Color(0xFF9999FF),
      500: Color(0xFFCCCCFF), // Base color
      600: Color(0xFF9999CC),
      700: Color(0xFF666699),
      800: Color(0xFF4D4D7A),
      900: Color(0xFF33335C),
    },
  );

  // Email Priority Colors
  static const Color priorityHigh = Color(0xFFEF4444);
  static const Color priorityMedium = Color(0xFFF59E0B);
  static const Color priorityLow = Color(0xFF10B981);
  static const Color priorityNone = Color(0xFF6B7280);

  // AI Insight Colors
  static const Color aiPrimary = Color(0xFF8B5CF6);
  static const Color aiSecondary = Color(0xFFA78BFA);
  static const Color aiBackground = Color(0xFFF3F4F6);
  static const Color aiAccent = Color(0xFF7C3AED);

  // Status Colors
  static const Color statusRead = Color(0xFF6B7280);
  static const Color statusUnread = Color(0xFF1F2937);
  static const Color statusImportant = Color(0xFFEF4444);
  static const Color statusStarred = Color(0xFFF59E0B);
  static const Color statusArchived = Color(0xFF9CA3AF);

  // Gradient Colors
  static const List<Color> primaryGradient = [
    Color(0xFFCCCCFF),
    Color(0xFF9999CC),
  ];

  static const List<Color> secondaryGradient = [
    Color(0xFFE6E6FA),
    Color(0xFFC8A2C8),
  ];

  static const List<Color> aiGradient = [
    Color(0xFF8B5CF6),
    Color(0xFFA78BFA),
  ];

  // Opacity variants
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  // Helper methods for common opacity levels
  static Color get periwinkleBase10 => periwinkleBase.withOpacity(0.1);
  static Color get periwinkleBase20 => periwinkleBase.withOpacity(0.2);
  static Color get periwinkleBase30 => periwinkleBase.withOpacity(0.3);
  static Color get periwinkleBase50 => periwinkleBase.withOpacity(0.5);

  static Color get textPrimary10 => textPrimary.withOpacity(0.1);
  static Color get textPrimary20 => textPrimary.withOpacity(0.2);
  static Color get textPrimary50 => textPrimary.withOpacity(0.5);

  // Context-specific colors
  static const Color emailListBackground = Color(0xFFFAFAFA);
  static const Color emailCardBackground = Color(0xFFFFFFFF);
  static const Color emailCardHover = Color(0xFFF5F5FF);
  static const Color emailCardSelected = Color(0xFFE6E6FF);

  static const Color sidebarBackground = Color(0xFFF9FAFB);
  static const Color sidebarHover = Color(0xFFF3F4F6);
  static const Color sidebarSelected = Color(0xFFE6E6FF);

  static const Color headerBackground = Color(0xFFFFFFFF);
  static const Color headerBorder = Color(0xFFE5E7EB);

  // Button colors
  static const Color buttonPrimary = Color(0xFF6A5ACD);
  static const Color buttonPrimaryHover = Color(0xFF5B4BC4);
  static const Color buttonSecondary = Color(0xFFE6E6FF);
  static const Color buttonSecondaryHover = Color(0xFFCCCCFF);
  static const Color buttonDanger = Color(0xFFEF4444);
  static const Color buttonDangerHover = Color(0xFFDC2626);

  // Input colors
  static const Color inputBackground = Color(0xFFFFFFFF);
  static const Color inputBorder = Color(0xFFE5E7EB);
  static const Color inputBorderFocus = Color(0xFF6A5ACD);
  static const Color inputBorderError = Color(0xFFEF4444);
  static const Color inputPlaceholder = Color(0xFF9CA3AF);

  // Loading colors
  static const Color loadingPrimary = Color(0xFFCCCCFF);
  static const Color loadingSecondary = Color(0xFFE6E6FF);
  static const Color skeletonBase = Color(0xFFE5E7EB);
  static const Color skeletonHighlight = Color(0xFFF3F4F6);
}
