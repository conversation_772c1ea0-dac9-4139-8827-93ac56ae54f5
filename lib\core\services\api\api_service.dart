import 'package:dio/dio.dart' hide Response;
import 'package:dio/src/response.dart';
import 'package:get/get.dart' hide Response;
import 'dio_client.dart';
import '../../constants/api_constants.dart';

class ApiService extends GetxService {
  late DioClient _dioClient;
  
  @override
  void onInit() {
    super.onInit();
    _dioClient = DioClient();
  }

  // Gmail API methods
  Future<Response> getMessages({
    String? query,
    int maxResults = 20,
    String? pageToken,
  }) async {
    return await _dioClient.get(
      '${ApiConstants.gmailBaseUrl}${ApiConstants.messagesEndpoint}',
      queryParameters: {
        'q': query,
        'maxResults': maxResults,
        if (pageToken != null) 'pageToken': pageToken,
      },
    );
  }

  Future<Response> getMessage(String messageId) async {
    return await _dioClient.get(
      '${ApiConstants.gmailBaseUrl}${ApiConstants.messagesEndpoint}/$messageId',
      queryParameters: {'format': 'full'},
    );
  }

  Future<Response> sendMessage(Map<String, dynamic> message) async {
    return await _dioClient.post(
      '${ApiConstants.gmailBaseUrl}${ApiConstants.messagesEndpoint}/send',
      data: message,
    );
  }

  Future<Response> getUserProfile() async {
    return await _dioClient.get(
      '${ApiConstants.gmailBaseUrl}${ApiConstants.profileEndpoint}',
    );
  }

  Future<Response> getLabels() async {
    return await _dioClient.get(
      '${ApiConstants.gmailBaseUrl}${ApiConstants.labelsEndpoint}',
    );
  }
}