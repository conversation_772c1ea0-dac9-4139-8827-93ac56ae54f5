import 'package:dio/dio.dart';
import 'package:get/get.dart' hide Response;
import 'dio_client.dart';
import '../../constants/api_constants.dart';
import '../logger/logger_service.dart';
import '../connectivity/connectivity_service.dart';

/// Enhanced API Service with retry logic, error handling, and rate limiting
class ApiService extends GetxService {
  static ApiService get instance => Get.find<ApiService>();

  late DioClient _dioClient;
  late ConnectivityService _connectivityService;

  // Rate limiting
  final Map<String, DateTime> _lastRequestTimes = {};
  final Duration _minRequestInterval = const Duration(milliseconds: 100);

  @override
  void onInit() {
    super.onInit();
    _dioClient = DioClient();
    _connectivityService = Get.find<ConnectivityService>();
    LoggerService.info('ApiService initialized', 'API');
  }

  /// Check if we can make a request (rate limiting)
  bool _canMakeRequest(String endpoint) {
    final lastTime = _lastRequestTimes[endpoint];
    if (lastTime == null) return true;

    final timeSinceLastRequest = DateTime.now().difference(lastTime);
    return timeSinceLastRequest >= _minRequestInterval;
  }

  /// Update last request time for rate limiting
  void _updateRequestTime(String endpoint) {
    _lastRequestTimes[endpoint] = DateTime.now();
  }

  /// Execute API request with retry logic and error handling
  Future<Response?> _executeRequest(
    Future<Response> Function() request,
    String endpoint, {
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
  }) async {
    // Check connectivity
    if (!_connectivityService.isConnected) {
      LoggerService.warning('No internet connection for request to $endpoint', 'API');
      throw DioException(
        requestOptions: RequestOptions(path: endpoint),
        type: DioExceptionType.connectionError,
        message: 'No internet connection',
      );
    }

    // Rate limiting
    if (!_canMakeRequest(endpoint)) {
      await Future.delayed(_minRequestInterval);
    }

    int attempts = 0;
    while (attempts < maxRetries) {
      try {
        _updateRequestTime(endpoint);
        final response = await request();
        LoggerService.debug('API request successful: $endpoint', 'API');
        return response;
      } catch (e) {
        attempts++;
        LoggerService.warning('API request failed (attempt $attempts/$maxRetries): $endpoint', 'API', e);

        if (attempts >= maxRetries) {
          LoggerService.error('API request failed after $maxRetries attempts: $endpoint', e, null, 'API');
          rethrow;
        }

        // Exponential backoff
        final delay = Duration(milliseconds: retryDelay.inMilliseconds * attempts);
        await Future.delayed(delay);
      }
    }

    return null;
  }

  // Gmail API methods with enhanced error handling

  /// Get messages from Gmail API
  Future<Response?> getMessages({
    String? query,
    int maxResults = 20,
    String? pageToken,
    List<String>? labelIds,
  }) async {
    final endpoint = '${ApiConstants.gmailBaseUrl}${ApiConstants.messagesEndpoint}';

    return await _executeRequest(
      () => _dioClient.get(
        endpoint,
        queryParameters: {
          if (query != null) 'q': query,
          'maxResults': maxResults,
          if (pageToken != null) 'pageToken': pageToken,
          if (labelIds != null && labelIds.isNotEmpty) 'labelIds': labelIds,
        },
      ),
      endpoint,
    );
  }

  /// Get a specific message by ID
  Future<Response?> getMessage(String messageId, {String format = 'full'}) async {
    final endpoint = '${ApiConstants.gmailBaseUrl}${ApiConstants.messagesEndpoint}/$messageId';

    return await _executeRequest(
      () => _dioClient.get(
        endpoint,
        queryParameters: {'format': format},
      ),
      endpoint,
    );
  }

  /// Send a message
  Future<Response?> sendMessage(Map<String, dynamic> message) async {
    final endpoint = '${ApiConstants.gmailBaseUrl}${ApiConstants.messagesEndpoint}/send';

    return await _executeRequest(
      () => _dioClient.post(endpoint, data: message),
      endpoint,
    );
  }

  /// Modify message labels
  Future<Response?> modifyMessage(
    String messageId, {
    List<String>? addLabelIds,
    List<String>? removeLabelIds,
  }) async {
    final endpoint = '${ApiConstants.gmailBaseUrl}${ApiConstants.messagesEndpoint}/$messageId/modify';

    return await _executeRequest(
      () => _dioClient.post(
        endpoint,
        data: {
          if (addLabelIds != null && addLabelIds.isNotEmpty) 'addLabelIds': addLabelIds,
          if (removeLabelIds != null && removeLabelIds.isNotEmpty) 'removeLabelIds': removeLabelIds,
        },
      ),
      endpoint,
    );
  }

  /// Get user profile
  Future<Response?> getUserProfile() async {
    final endpoint = '${ApiConstants.gmailBaseUrl}${ApiConstants.profileEndpoint}';

    return await _executeRequest(
      () => _dioClient.get(endpoint),
      endpoint,
    );
  }

  /// Get all labels
  Future<Response?> getLabels() async {
    final endpoint = '${ApiConstants.gmailBaseUrl}${ApiConstants.labelsEndpoint}';

    return await _executeRequest(
      () => _dioClient.get(endpoint),
      endpoint,
    );
  }

  /// Create a new label
  Future<Response?> createLabel(Map<String, dynamic> labelData) async {
    final endpoint = '${ApiConstants.gmailBaseUrl}${ApiConstants.labelsEndpoint}';

    return await _executeRequest(
      () => _dioClient.post(endpoint, data: labelData),
      endpoint,
    );
  }

  /// Get message threads
  Future<Response?> getThreads({
    String? query,
    int maxResults = 20,
    String? pageToken,
  }) async {
    final endpoint = '${ApiConstants.gmailBaseUrl}${ApiConstants.threadsEndpoint}';

    return await _executeRequest(
      () => _dioClient.get(
        endpoint,
        queryParameters: {
          if (query != null) 'q': query,
          'maxResults': maxResults,
          if (pageToken != null) 'pageToken': pageToken,
        },
      ),
      endpoint,
    );
  }

  /// Get a specific thread
  Future<Response?> getThread(String threadId, {String format = 'full'}) async {
    final endpoint = '${ApiConstants.gmailBaseUrl}${ApiConstants.threadsEndpoint}/$threadId';

    return await _executeRequest(
      () => _dioClient.get(
        endpoint,
        queryParameters: {'format': format},
      ),
      endpoint,
    );
  }

  /// Get attachment data
  Future<Response?> getAttachment(String messageId, String attachmentId) async {
    final endpoint = '${ApiConstants.gmailBaseUrl}${ApiConstants.messagesEndpoint}/$messageId/attachments/$attachmentId';

    return await _executeRequest(
      () => _dioClient.get(endpoint),
      endpoint,
    );
  }

  /// Batch request for multiple operations
  Future<List<Response?>> batchRequests(List<Future<Response?> Function()> requests) async {
    LoggerService.info('Executing batch request with ${requests.length} operations', 'API');

    try {
      final results = await Future.wait(
        requests.map((request) => request()),
        eagerError: false,
      );

      LoggerService.info('Batch request completed successfully', 'API');
      return results;
    } catch (e) {
      LoggerService.error('Batch request failed', e, null, 'API');
      rethrow;
    }
  }

  /// Clear rate limiting cache
  void clearRateLimit() {
    _lastRequestTimes.clear();
    LoggerService.debug('Rate limiting cache cleared', 'API');
  }
}