import 'package:flutter/material.dart';
import 'app_colors.dart';

/// App typography system based on Inter font family
class AppTypography {
  AppTypography._();

  // Font families
  static const String primaryFont = 'Inter';
  static const String secondaryFont = 'SF Pro'; // iOS
  static const String monospaceFont = 'JetBrains Mono';

  // Font weights
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;

  // Display styles
  static const TextStyle displayLarge = TextStyle(
    fontFamily: primaryFont,
    fontSize: 32,
    height: 1.25, // 40px line height
    fontWeight: bold,
    color: AppColors.textPrimary,
    letterSpacing: -0.5,
  );

  static const TextStyle displayMedium = TextStyle(
    fontFamily: primaryFont,
    fontSize: 28,
    height: 1.29, // 36px line height
    fontWeight: bold,
    color: AppColors.textPrimary,
    letterSpacing: -0.25,
  );

  static const TextStyle displaySmall = TextStyle(
    fontFamily: primaryFont,
    fontSize: 24,
    height: 1.33, // 32px line height
    fontWeight: semiBold,
    color: AppColors.textPrimary,
    letterSpacing: 0,
  );

  // Heading styles
  static const TextStyle heading1 = TextStyle(
    fontFamily: primaryFont,
    fontSize: 22,
    height: 1.27, // 28px line height
    fontWeight: semiBold,
    color: AppColors.textPrimary,
    letterSpacing: 0,
  );

  static const TextStyle heading2 = TextStyle(
    fontFamily: primaryFont,
    fontSize: 20,
    height: 1.4, // 28px line height
    fontWeight: semiBold,
    color: AppColors.textPrimary,
    letterSpacing: 0,
  );

  static const TextStyle heading3 = TextStyle(
    fontFamily: primaryFont,
    fontSize: 18,
    height: 1.33, // 24px line height
    fontWeight: semiBold,
    color: AppColors.textPrimary,
    letterSpacing: 0,
  );

  // Body styles
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: primaryFont,
    fontSize: 16,
    height: 1.5, // 24px line height
    fontWeight: regular,
    color: AppColors.textPrimary,
    letterSpacing: 0,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontFamily: primaryFont,
    fontSize: 14,
    height: 1.43, // 20px line height
    fontWeight: regular,
    color: AppColors.textPrimary,
    letterSpacing: 0,
  );

  static const TextStyle bodySmall = TextStyle(
    fontFamily: primaryFont,
    fontSize: 12,
    height: 1.33, // 16px line height
    fontWeight: regular,
    color: AppColors.textPrimary,
    letterSpacing: 0,
  );

  // Label styles
  static const TextStyle labelLarge = TextStyle(
    fontFamily: primaryFont,
    fontSize: 14,
    height: 1.43, // 20px line height
    fontWeight: medium,
    color: AppColors.textSecondary,
    letterSpacing: 0.1,
  );

  static const TextStyle labelMedium = TextStyle(
    fontFamily: primaryFont,
    fontSize: 12,
    height: 1.33, // 16px line height
    fontWeight: medium,
    color: AppColors.textSecondary,
    letterSpacing: 0.5,
  );

  static const TextStyle labelSmall = TextStyle(
    fontFamily: primaryFont,
    fontSize: 11,
    height: 1.45, // 16px line height
    fontWeight: medium,
    color: AppColors.textSecondary,
    letterSpacing: 0.5,
  );

  // Caption and overline
  static const TextStyle caption = TextStyle(
    fontFamily: primaryFont,
    fontSize: 11,
    height: 1.45, // 16px line height
    fontWeight: regular,
    color: AppColors.textTertiary,
    letterSpacing: 0.4,
  );

  static const TextStyle overline = TextStyle(
    fontFamily: primaryFont,
    fontSize: 10,
    height: 1.6, // 16px line height
    fontWeight: medium,
    color: AppColors.textTertiary,
    letterSpacing: 1.5,
  );

  // Specialized styles
  static const TextStyle button = TextStyle(
    fontFamily: primaryFont,
    fontSize: 14,
    height: 1.43, // 20px line height
    fontWeight: medium,
    letterSpacing: 0.1,
  );

  static const TextStyle link = TextStyle(
    fontFamily: primaryFont,
    fontSize: 14,
    height: 1.43, // 20px line height
    fontWeight: medium,
    color: AppColors.royalBlue,
    decoration: TextDecoration.underline,
    letterSpacing: 0,
  );

  static const TextStyle code = TextStyle(
    fontFamily: monospaceFont,
    fontSize: 13,
    height: 1.54, // 20px line height
    fontWeight: regular,
    color: AppColors.textPrimary,
    backgroundColor: AppColors.periwinkleLight,
    letterSpacing: 0,
  );

  // Email-specific styles
  static const TextStyle emailSubject = TextStyle(
    fontFamily: primaryFont,
    fontSize: 16,
    height: 1.5, // 24px line height
    fontWeight: medium,
    color: AppColors.textPrimary,
    letterSpacing: 0,
  );

  static const TextStyle emailSender = TextStyle(
    fontFamily: primaryFont,
    fontSize: 14,
    height: 1.43, // 20px line height
    fontWeight: medium,
    color: AppColors.textSecondary,
    letterSpacing: 0,
  );

  static const TextStyle emailPreview = TextStyle(
    fontFamily: primaryFont,
    fontSize: 14,
    height: 1.43, // 20px line height
    fontWeight: regular,
    color: AppColors.textTertiary,
    letterSpacing: 0,
  );

  static const TextStyle emailTimestamp = TextStyle(
    fontFamily: primaryFont,
    fontSize: 12,
    height: 1.33, // 16px line height
    fontWeight: regular,
    color: AppColors.textTertiary,
    letterSpacing: 0,
  );

  // AI-specific styles
  static const TextStyle aiInsight = TextStyle(
    fontFamily: primaryFont,
    fontSize: 14,
    height: 1.43, // 20px line height
    fontWeight: medium,
    color: AppColors.aiPrimary,
    letterSpacing: 0,
  );

  static const TextStyle aiSuggestion = TextStyle(
    fontFamily: primaryFont,
    fontSize: 13,
    height: 1.54, // 20px line height
    fontWeight: regular,
    color: AppColors.aiSecondary,
    letterSpacing: 0,
  );

  // Dark mode variants
  static TextStyle get displayLargeDark => displayLarge.copyWith(color: AppColors.textPrimaryDark);
  static TextStyle get displayMediumDark => displayMedium.copyWith(color: AppColors.textPrimaryDark);
  static TextStyle get displaySmallDark => displaySmall.copyWith(color: AppColors.textPrimaryDark);
  static TextStyle get heading1Dark => heading1.copyWith(color: AppColors.textPrimaryDark);
  static TextStyle get heading2Dark => heading2.copyWith(color: AppColors.textPrimaryDark);
  static TextStyle get heading3Dark => heading3.copyWith(color: AppColors.textPrimaryDark);
  static TextStyle get bodyLargeDark => bodyLarge.copyWith(color: AppColors.textPrimaryDark);
  static TextStyle get bodyMediumDark => bodyMedium.copyWith(color: AppColors.textPrimaryDark);
  static TextStyle get bodySmallDark => bodySmall.copyWith(color: AppColors.textPrimaryDark);
  static TextStyle get labelLargeDark => labelLarge.copyWith(color: AppColors.textSecondaryDark);
  static TextStyle get labelMediumDark => labelMedium.copyWith(color: AppColors.textSecondaryDark);
  static TextStyle get labelSmallDark => labelSmall.copyWith(color: AppColors.textSecondaryDark);

  // Helper methods
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }

  static TextStyle withWeight(TextStyle style, FontWeight weight) {
    return style.copyWith(fontWeight: weight);
  }

  static TextStyle withSize(TextStyle style, double size) {
    return style.copyWith(fontSize: size);
  }
}
