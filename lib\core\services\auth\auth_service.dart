import 'package:get/get.dart';
import '../../models/user_model.dart';
import '../storage/local_storage_service.dart';
import '../../constants/app_constants.dart';

class AuthService extends GetxService {
  final LocalStorageService _storage = Get.find<LocalStorageService>();
  
  final Rx<UserModel?> _currentUser = Rx<UserModel?>(null);
  UserModel? get currentUser => _currentUser.value;
  
  final RxBool _isAuthenticated = false.obs;
  bool get isAuthenticated => _isAuthenticated.value;

  @override
  void onInit() {
    super.onInit();
    _loadUserFromStorage();
  }

  void _loadUserFromStorage() {
    final userData = _storage.read(AppConstants.userDataKey);
    if (userData != null) {
      _currentUser.value = UserModel.fromJson(userData);
      _isAuthenticated.value = true;
    }
  }

  Future<void> login(UserModel user) async {
    _currentUser.value = user;
    _isAuthenticated.value = true;
    await _storage.write(AppConstants.userDataKey, user.toJson());
  }

  Future<void> logout() async {
    _currentUser.value = null;
    _isAuthenticated.value = false;
    await _storage.remove(AppConstants.userDataKey);
    await _storage.remove(AppConstants.userTokenKey);
  }

  Future<void> updateUser(UserModel user) async {
    _currentUser.value = user;
    await _storage.write(AppConstants.userDataKey, user.toJson());
  }
}