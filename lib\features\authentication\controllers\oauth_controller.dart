import 'package:get/get.dart';
import '../../../core/services/auth/oauth_service.dart';
import '../../../core/services/auth/auth_service.dart';
import '../../../core/models/user_model.dart';

class OauthController extends GetxController {
  final OAuthService _oauthService = Get.find<OAuthService>();
  final AuthService _authService = Get.find<AuthService>();
  
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _checkExistingAuth();
  }

  Future<void> _checkExistingAuth() async {
    try {
      isLoading.value = true;
      
      // Try silent sign in first
      final user = await _oauthService.silentSignIn();
      if (user != null) {
        await _authService.login(user);
        _navigateToHome();
      }
    } catch (e) {
      print('Silent sign in failed: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> signInWithGoogle() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      
      final user = await _oauthService.signInWithGoogle();
      
      if (user != null) {
        await _authService.login(user);
        Get.snackbar(
          'Success', 
          'Welcome ${user.name}!',
          snackPosition: SnackPosition.BOTTOM,
        );
        _navigateToHome();
      } else {
        errorMessage.value = 'Sign in was cancelled or failed';
      }
    } catch (e) {
      errorMessage.value = 'Failed to sign in: ${e.toString()}';
      Get.snackbar(
        'Error', 
        errorMessage.value,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> signOut() async {
    try {
      isLoading.value = true;
      
      await _oauthService.signOut();
      await _authService.logout();
      
      Get.snackbar(
        'Success', 
        'Signed out successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
      
      _navigateToLogin();
    } catch (e) {
      Get.snackbar(
        'Error', 
        'Failed to sign out: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  void _navigateToHome() {
    // Navigate to dashboard/home screen
    Get.offAllNamed('/dashboard');
  }

  void _navigateToLogin() {
    // Navigate to login screen
    Get.offAllNamed('/login');
  }

  void clearError() {
    errorMessage.value = '';
  }
}