import 'package:get/get.dart';
import 'app_routes.dart';
import '../../../features/authentication/presentation/screens/splash_screen.dart';
import '../../../features/authentication/presentation/screens/login_screen.dart';
import '../../../features/onboarding/presentation/screens/welcome_screen.dart';
import '../../../features/dashboard/presentation/screens/dashboard_screen.dart';
import '../../../features/inbox/presentation/screens/inbox_screen.dart';

class AppPages {
  static final routes = [
    GetPage(
      name: AppRoutes.splash,
      page: () => SplashScreen(),
    ),
    GetPage(
      name: AppRoutes.login,
      page: () => LoginScreen(),
    ),
    GetPage(
      name: AppRoutes.onboarding,
      page: () => WelcomeScreen(),
    ),
    GetPage(
      name: AppRoutes.dashboard,
      page: () => DashboardScreen(),
    ),
    GetPage(
      name: AppRoutes.inbox,
      page: () => InboxScreen(),
    ),
  ];
}