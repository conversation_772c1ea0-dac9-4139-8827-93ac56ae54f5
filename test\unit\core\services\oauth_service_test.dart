import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:google_sign_in/google_sign_in.dart';

import '../../../../lib/core/services/auth/oauth_service.dart';
import '../../../../lib/core/services/storage/secure_storage_service.dart';

// Generate mocks
@GenerateMocks([GoogleSignIn, GoogleSignInAccount, GoogleSignInAuthentication])
import 'oauth_service_test.mocks.dart';

void main() {
  group('OAuthService Tests', () {
    late OAuthService oauthService;
    late MockGoogleSignIn mockGoogleSignIn;
    late MockGoogleSignInAccount mockAccount;
    late MockGoogleSignInAuthentication mockAuth;

    setUp(() {
      Get.testMode = true;
      oauthService = OAuthService();
      mockGoogleSignIn = MockGoogleSignIn();
      mockAccount = MockGoogleSignInAccount();
      mockAuth = MockGoogleSignInAuthentication();
    });

    tearDown(() {
      Get.reset();
    });

    test('should return null when user cancels sign in', () async {
      // Arrange
      when(mockGoogleSignIn.signIn()).thenAnswer((_) async => null);

      // Act
      final result = await oauthService.signInWithGoogle();

      // Assert
      expect(result, isNull);
    });

    test('should return user model on successful sign in', () async {
      // Arrange
      when(mockGoogleSignIn.signIn()).thenAnswer((_) async => mockAccount);
      when(mockAccount.authentication).thenAnswer((_) async => mockAuth);
      when(mockAccount.id).thenReturn('test_id');
      when(mockAccount.email).thenReturn('<EMAIL>');
      when(mockAccount.displayName).thenReturn('Test User');
      when(mockAccount.photoUrl).thenReturn('https://example.com/photo.jpg');
      when(mockAuth.accessToken).thenReturn('test_access_token');
      when(mockAuth.idToken).thenReturn('test_id_token');

      // Act
      final result = await oauthService.signInWithGoogle();

      // Assert
      expect(result, isNotNull);
      expect(result!.email, equals('<EMAIL>'));
      expect(result.name, equals('Test User'));
    });

    test('should handle sign in errors gracefully', () async {
      // Arrange
      when(mockGoogleSignIn.signIn()).thenThrow(Exception('Sign in failed'));

      // Act
      final result = await oauthService.signInWithGoogle();

      // Assert
      expect(result, isNull);
    });
  });
}