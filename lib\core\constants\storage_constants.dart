class StorageConstants {
  // Secure Storage Keys (for sensitive data)
  static const String accessToken = 'access_token';
  static const String refreshToken = 'refresh_token';
  static const String idToken = 'id_token';
  static const String biometricEnabled = 'biometric_enabled';
  static const String pinCode = 'pin_code';
  
  // Local Storage Keys (for app data)
  static const String userPreferences = 'user_preferences';
  static const String themeMode = 'theme_mode';
  static const String languageCode = 'language_code';
  static const String notificationSettings = 'notification_settings';
  static const String syncSettings = 'sync_settings';
  static const String cacheSettings = 'cache_settings';
  static const String lastSyncTime = 'last_sync_time';
  static const String offlineMode = 'offline_mode';
  
  // Database Tables
  static const String usersTable = 'users';
  static const String emailsTable = 'emails';
  static const String attachmentsTable = 'attachments';
  static const String labelsTable = 'labels';
  static const String aiInsightsTable = 'ai_insights';
  static const String calendarEventsTable = 'calendar_events';
  static const String searchIndexTable = 'email_search';
}