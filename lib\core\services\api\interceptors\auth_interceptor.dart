import 'package:dio/dio.dart';
import 'package:get/get.dart';
import '../../storage/local_storage_service.dart';
import '../../../constants/app_constants.dart';

class AuthInterceptor extends Interceptor {
  final LocalStorageService _storage = Get.find<LocalStorageService>();

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final token = _storage.read(AppConstants.userTokenKey);
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (err.response?.statusCode == 401) {
      // Token expired, handle refresh or logout
      _handleUnauthorized();
    }
    handler.next(err);
  }

  void _handleUnauthorized() {
    // Clear stored tokens and redirect to login
    _storage.remove(AppConstants.userTokenKey);
    _storage.remove(AppConstants.userDataKey);
    Get.offAllNamed('/login');
  }
}