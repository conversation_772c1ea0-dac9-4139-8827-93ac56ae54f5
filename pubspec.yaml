name: ai_email_client
description: "AI-powered email client that transforms G<PERSON> into an intelligent personal assistant"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.5.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # State Management & Architecture
  get: ^4.7.2
  
  # Firebase
  firebase_core: ^3.8.0
  firebase_auth: ^5.5.1
  firebase_messaging: ^15.2.5
  
  # Networking
  dio: ^5.8.0
  connectivity_plus: ^6.1.4
  
  # Storage
  get_storage: ^2.1.1
  flutter_secure_storage: ^9.2.4
  sqflite: ^2.3.0
  
  # UI & Design
  flutter_screenutil: ^5.9.3
  cached_network_image: ^3.4.1
  skeletonizer: ^2.0.1
  flutter_spinkit: ^5.2.1
  
  # Media
  video_player: ^2.9.5
  just_audio: ^0.10.4
  image_picker: ^1.1.2
  file_picker: ^10.2.0
  youtube_player_iframe: ^5.2.1
  
  # Rich Text
  flutter_quill: ^11.4.2
  flutter_html: ^3.0.0
  markdown: ^7.3.0
  
  # Location
  geolocator: ^14.0.1
  geocoding: ^4.0.0
  permission_handler: ^12.0.0
  
  # Authentication
  google_sign_in: ^7.1.0
  pin_code_fields: ^8.0.1
  otp_autofill: ^4.0.1
  crypto: ^3.0.3
  
  # Notifications
  flutter_local_notifications: ^19.1.0
  
  # Utilities
  share_plus: ^11.0.0
  device_info_plus: ^11.4.0
  url_launcher: ^6.2.5
  timezone: ^0.10.1
  uuid: ^4.3.3
  logger: ^2.5.0
  
  # Internationalization
  intl: ^0.20.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  mockito: ^5.4.4
  build_runner: ^2.4.0
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/images/onboarding/
    - assets/images/illustrations/
    - assets/images/icons/
    - assets/fonts/
    - assets/animations/
    - assets/translations/
  
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700