import 'package:dio/dio.dart';
import 'package:get/get.dart';
import '../logger/logger_service.dart';

/// Centralized error handling service
class ErrorHandlerService extends GetxService {
  static ErrorHandlerService get instance => Get.find<ErrorHandlerService>();

  @override
  void onInit() {
    super.onInit();
    LoggerService.info('ErrorHandlerService initialized', 'ERROR');
  }

  /// Handle API errors
  void handleApiError(dynamic error, {String? context}) {
    if (error is DioException) {
      _handleDioError(error, context);
    } else {
      _handleGenericError(error, context);
    }
  }

  /// Handle Dio specific errors
  void _handleDioError(DioException error, String? context) {
    String message;
    String userMessage;

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        message = 'Connection timeout';
        userMessage = 'Connection timeout. Please check your internet connection.';
        break;
      case DioExceptionType.sendTimeout:
        message = 'Send timeout';
        userMessage = 'Request timeout. Please try again.';
        break;
      case DioExceptionType.receiveTimeout:
        message = 'Receive timeout';
        userMessage = 'Server response timeout. Please try again.';
        break;
      case DioExceptionType.badResponse:
        message = 'Bad response: ${error.response?.statusCode}';
        userMessage = _getStatusCodeMessage(error.response?.statusCode);
        break;
      case DioExceptionType.cancel:
        message = 'Request cancelled';
        userMessage = 'Request was cancelled.';
        break;
      case DioExceptionType.connectionError:
        message = 'Connection error';
        userMessage = 'No internet connection. Please check your network.';
        break;
      case DioExceptionType.badCertificate:
        message = 'Bad certificate';
        userMessage = 'Security certificate error. Please try again.';
        break;
      case DioExceptionType.unknown:
      default:
        message = 'Unknown error: ${error.message}';
        userMessage = 'Something went wrong. Please try again.';
    }

    LoggerService.error(
      '${context != null ? '[$context] ' : ''}API Error: $message',
      error,
      error.stackTrace,
      'ERROR',
    );

    _showUserError(userMessage);
  }

  /// Handle generic errors
  void _handleGenericError(dynamic error, String? context) {
    final message = error.toString();
    LoggerService.error(
      '${context != null ? '[$context] ' : ''}Generic Error: $message',
      error,
      null,
      'ERROR',
    );

    _showUserError('An unexpected error occurred. Please try again.');
  }

  /// Get user-friendly message for HTTP status codes
  String _getStatusCodeMessage(int? statusCode) {
    switch (statusCode) {
      case 400:
        return 'Bad request. Please check your input.';
      case 401:
        return 'Unauthorized. Please login again.';
      case 403:
        return 'Access forbidden. You don\'t have permission.';
      case 404:
        return 'Resource not found.';
      case 409:
        return 'Conflict. The resource already exists.';
      case 422:
        return 'Invalid data. Please check your input.';
      case 429:
        return 'Too many requests. Please wait and try again.';
      case 500:
        return 'Server error. Please try again later.';
      case 502:
        return 'Bad gateway. Please try again later.';
      case 503:
        return 'Service unavailable. Please try again later.';
      case 504:
        return 'Gateway timeout. Please try again later.';
      default:
        return 'Something went wrong. Please try again.';
    }
  }

  /// Show error message to user
  void _showUserError(String message) {
    Get.snackbar(
      'Error',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Get.theme.colorScheme.error,
      colorText: Get.theme.colorScheme.onError,
      duration: const Duration(seconds: 4),
    );
  }

  /// Handle validation errors
  void handleValidationError(Map<String, List<String>> errors) {
    final firstError = errors.values.first.first;
    LoggerService.warning('Validation Error: $firstError', 'ERROR');
    _showUserError(firstError);
  }

  /// Handle authentication errors
  void handleAuthError(String message) {
    LoggerService.warning('Auth Error: $message', 'ERROR');
    _showUserError('Authentication failed. Please login again.');
    
    // Redirect to login
    Get.offAllNamed('/login');
  }

  /// Handle network errors
  void handleNetworkError() {
    LoggerService.warning('Network Error: No internet connection', 'ERROR');
    _showUserError('No internet connection. Please check your network.');
  }

  /// Handle permission errors
  void handlePermissionError(String permission) {
    LoggerService.warning('Permission Error: $permission', 'ERROR');
    _showUserError('Permission denied. Please grant $permission permission.');
  }

  /// Handle storage errors
  void handleStorageError(String operation) {
    LoggerService.error('Storage Error: Failed to $operation', null, null, 'ERROR');
    _showUserError('Storage error. Please try again.');
  }

  /// Handle AI processing errors
  void handleAiError(String operation) {
    LoggerService.error('AI Error: Failed to $operation', null, null, 'ERROR');
    _showUserError('AI processing failed. Please try again.');
  }

  /// Show success message
  void showSuccess(String message) {
    Get.snackbar(
      'Success',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Get.theme.colorScheme.primary,
      colorText: Get.theme.colorScheme.onPrimary,
      duration: const Duration(seconds: 3),
    );
  }

  /// Show info message
  void showInfo(String message) {
    Get.snackbar(
      'Info',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Get.theme.colorScheme.surface,
      colorText: Get.theme.colorScheme.onSurface,
      duration: const Duration(seconds: 3),
    );
  }

  /// Show warning message
  void showWarning(String message) {
    Get.snackbar(
      'Warning',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Get.theme.colorScheme.secondary,
      colorText: Get.theme.colorScheme.onSecondary,
      duration: const Duration(seconds: 3),
    );
  }
}
