import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/oauth_controller.dart';

class SplashScreen extends StatefulWidget {
  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    // Wait for a moment to show splash screen
    await Future.delayed(Duration(seconds: 2));
    
    // Check authentication status
    final OauthController authController = Get.put(OauthController());
    
    // Wait for auth check to complete
    await Future.delayed(Duration(milliseconds: 500));
    
    // Navigate based on auth status
    if (Get.find<OauthController>().isLoading.value) {
      // Still loading, wait a bit more
      await Future.delayed(Duration(seconds: 1));
    }
    
    // Navigate to login screen (controller will handle auto-login if user is already signed in)
    Get.offAllNamed('/login');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.blue,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.email,
              size: 100,
              color: Colors.white,
            ),
            SizedBox(height: 24),
            Text(
              'AI Email Client',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Powered by AI',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
            ),
            SizedBox(height: 48),
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}